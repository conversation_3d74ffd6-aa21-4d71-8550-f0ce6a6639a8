#Requires -Version 5.0
<#
.SYNOPSIS
    Advanced PowerShell-based recon and vulnerability enumeration script for ethical penetration testing.
.DESCRIPTION
    This script performs comprehensive reconnaissance tasks against a target domain,
    including checking robots.txt, HTTP headers, CORS, API endpoints, JS files,
    XSS reflection, exposed files, admin panels, SSL/TLS certificates,
    technology detection, subdomain enumeration, and more.
    Results are logged to console and a timestamped file.

    IMPORTANT: This script is intended for authorized security testing only.
    Always ensure you have explicit permission to scan the target domain.
.PARAMETER Target
    The target URL (e.g., "https://example.com"). Defaults to "https://liberty.baby/".
.PARAMETER Intensity
    The scan intensity level (1-3). Higher levels perform more aggressive tests.
    Default is 1 (basic scan).
.PARAMETER SkipSubdomains
    Skip subdomain enumeration to reduce scan time and network traffic.
.PARAMETER OutputDir
    Directory to save output files. Defaults to current directory.
.EXAMPLE
    .\Recon-Script-Fixed.ps1
.EXAMPLE
    .\Recon-Script-Fixed.ps1 -Target "https://another-site.com"
.EXAMPLE
    .\Recon-Script-Fixed.ps1 -Target "https://example.com" -Intensity 2 -OutputDir "C:\Reports"
.NOTES
    Author: Security Researcher
    Version: 2.1
    Last Updated: 2024-05-28

    This script is for educational purposes and authorized penetration testing only.
    Unauthorized scanning may violate laws and terms of service.
#>
param(
    [string]$Target = "https://liberty.baby/",
    [ValidateRange(1, 3)]
    [int]$Intensity = 1,
    [switch]$SkipSubdomains,
    [string]$OutputDir = (Get-Location).Path
)

# --- Initial Setup ---
Write-Host "=== Advanced Recon Started ===" -ForegroundColor Cyan

# Ensure Target URL ends with a slash for consistency
if (-not $Target.EndsWith("/")) {
    $Target += "/"
}

# Create output directory if it doesn't exist
if (-not (Test-Path -Path $OutputDir)) {
    try {
        New-Item -Path $OutputDir -ItemType Directory -Force | Out-Null
        Write-Host "Created output directory: $OutputDir" -ForegroundColor Green
    } catch {
        Write-Host "Error creating output directory. Using current directory instead." -ForegroundColor Red
        $OutputDir = (Get-Location).Path
    }
}

$Timestamp = Get-Date -Format "yyyyMMddHHmmss"
$LogFile = Join-Path -Path $OutputDir -ChildPath "ReconLog_$Timestamp.txt"
$StartTime = Get-Date
$FoundVulnerabilities = @()
$TechStack = @()

# Load required .NET assemblies
Add-Type -AssemblyName System.Web
Add-Type -AssemblyName System.Net.Http

# Helper function for logging
function Write-Log {
    param(
        [string]$Message,
        [System.ConsoleColor]$Color = [System.ConsoleColor]::Gray,
        [switch]$NoNewLine,
        [switch]$SectionTitle,
        [switch]$Vulnerability
    )

    # Format timestamp for console output
    $timestamp = Get-Date -Format "HH:mm:ss"

    if ($SectionTitle) {
        $formattedMessage = "`n--- $($Message.ToUpperInvariant()) ---"
        Write-Host "$timestamp $formattedMessage" -ForegroundColor Yellow
        Add-Content -Path $LogFile -Value "$formattedMessage"
    } else {
        Write-Host "$timestamp $Message" -ForegroundColor $Color -NoNewline:$NoNewLine
        Add-Content -Path $LogFile -Value $Message -NoNewline:$NoNewLine
    }

    if (-not $NoNewLine) {
        Add-Content -Path $LogFile -Value "" # Ensure newline in file if Write-Host added one
    }

    # Track vulnerabilities for summary
    if ($Vulnerability) {
        $script:FoundVulnerabilities += $Message
    }
}

# Function to make HTTP requests with error handling and timeouts
function Invoke-SafeWebRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [int]$TimeoutSec = 10,
        [switch]$UseBasicParsing,
        [switch]$AllowRedirection,
        [switch]$ReturnResponseOnError
    )

    # Add User-Agent if not specified
    if (-not $Headers.ContainsKey("User-Agent")) {
        $Headers["User-Agent"] = $UserAgent
    }

    try {
        $params = @{
            Uri = $Uri
            Method = $Method
            Headers = $Headers
            TimeoutSec = $TimeoutSec
            UseBasicParsing = $true
            ErrorAction = "Stop"
        }

        if ($AllowRedirection) {
            $params["MaximumRedirection"] = 5
        } else {
            $params["MaximumRedirection"] = 0
        }

        $response = Invoke-WebRequest @params
        return $response
    }
    catch {
        if ($ReturnResponseOnError -and $_.Exception.Response) {
            return $_.Exception.Response
        }

        # Return custom object with error info
        return [PSCustomObject]@{
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
            StatusDescription = if ($_.Exception.Response) { $_.Exception.Response.StatusDescription } else { "Error" }
            Error = $_.Exception.Message
            Headers = if ($_.Exception.Response) { $_.Exception.Response.Headers } else { $null }
        }
    }
}

# Set TLS protocols for compatibility (PowerShell 5.1 might need this explicitly)
try {
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls13, [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
} catch {
    try {
        # Fallback if TLS 1.3 is not available
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
    } catch {
        Write-Log "[WARNING] Could not set TLS protocols. Modern defaults will be used." -Color Yellow
    }
}

# Increase connection limit to allow more parallel requests
[System.Net.ServicePointManager]::DefaultConnectionLimit = 100

# User Agent rotation for evasion (if needed)
$UserAgents = @(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0"
)
$UserAgent = $UserAgents[0]  # Default to first one, can rotate if needed

# Banner
$banner = @"
╔═══════════════════════════════════════════════════════════════════════════╗
║                                                                           ║
║   █▀█ █▀▀ █▀▀ █▀█ █▄░█   █▀▀ █▄░█ █▀▀ █ █▄░█ █▀▀                         ║
║   █▀▄ ██▄ █▄▄ █▄█ █░▀█   ██▄ █░▀█ █▄█ █ █░▀█ ██▄                         ║
║                                                                           ║
║   Advanced PowerShell Reconnaissance & Vulnerability Scanner v2.1         ║
║   Target: $($Target.PadRight(58)) ║
║   Scan Level: $($Intensity) - $(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"})$(if($Intensity -eq 1){" ".PadRight(50)}elseif($Intensity -eq 2){" ".PadRight(47)}else{" ".PadRight(46)}) ║
║                                                                           ║
╚═══════════════════════════════════════════════════════════════════════════╝
"@

Write-Host $banner -ForegroundColor Cyan

Write-Log "Target: $Target"
Write-Log "Log File: $LogFile"
Write-Log "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Log "Scan Intensity: $Intensity ($(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"}))"
Add-Content -Path $LogFile -Value "Target: $Target"
Add-Content -Path $LogFile -Value "Log File: $LogFile"
Add-Content -Path $LogFile -Value "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Add-Content -Path $LogFile -Value "Scan Intensity: $Intensity ($(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"}))"

# --- Basic Domain Info ---
Write-Log "Basic Domain Info" -SectionTitle
try {
    $uri = New-Object System.Uri($Target)
    Write-Log "Domain: $($uri.Host)" -Color Cyan
    Write-Log "Scheme: $($uri.Scheme)" -Color Cyan
    Write-Log "Port: $($uri.Port)" -Color Cyan

    # Get IP addresses (IPv4 and IPv6)
    try {
        $ipAddresses = [System.Net.Dns]::GetHostAddresses($uri.Host)
        $ipv4Addresses = $ipAddresses | Where-Object { $_.AddressFamily -eq 'InterNetwork' } | ForEach-Object { $_.IPAddressToString }
        $ipv6Addresses = $ipAddresses | Where-Object { $_.AddressFamily -eq 'InterNetworkV6' } | ForEach-Object { $_.IPAddressToString }

        if ($ipv4Addresses) {
            Write-Log "IPv4 Addresses:" -Color Green
            foreach ($ip in $ipv4Addresses) {
                Write-Log "  - $ip" -Color Green
            }
        } else {
            Write-Log "No IPv4 addresses found." -Color Yellow
        }

        if ($ipv6Addresses) {
            Write-Log "IPv6 Addresses:" -Color Green
            foreach ($ip in $ipv6Addresses) {
                Write-Log "  - $ip" -Color Green
            }
        }
    } catch {
        Write-Log "Error resolving IP addresses: $($_.Exception.Message)" -Color Red
    }

    # ICMP ping test
    try {
        $pingTest = Test-Connection -ComputerName $uri.Host -Count 2 -Quiet
        if ($pingTest) {
            Write-Log "Domain responds to ICMP (ping)." -Color Green
        } else {
            Write-Log "Domain does not respond to ICMP (ping) or ICMP is blocked." -Color Yellow
        }
    } catch {
        Write-Log "Error during ping test: $($_.Exception.Message)" -Color Yellow
    }

    # HTTP connectivity test
    try {
        $basicRequest = Invoke-SafeWebRequest -Uri $Target -TimeoutSec 10 -AllowRedirection
        if ($basicRequest.StatusCode -ge 200 -and $basicRequest.StatusCode -lt 400) {
            Write-Log "Domain resolves and responds to HTTP(S) requests. Status: $($basicRequest.StatusCode)" -Color Green

            # Check for redirects
            if ($basicRequest.BaseResponse.ResponseUri -and $basicRequest.BaseResponse.ResponseUri.AbsoluteUri -ne $Target) {
                Write-Log "  [!] Request was redirected to: $($basicRequest.BaseResponse.ResponseUri.AbsoluteUri)" -Color Yellow
            }

            # Check page title
            if ($basicRequest.Content -match '<title>(.*?)</title>') {
                $pageTitle = $matches[1]
                Write-Log "  - Page Title: $pageTitle" -Color Cyan
            }

            # Check page size
            $contentLength = if ($basicRequest.Headers -and $basicRequest.Headers["Content-Length"]) {
                $basicRequest.Headers["Content-Length"]
            } else {
                $basicRequest.Content.Length
            }
            Write-Log "  - Content Length: $contentLength bytes" -Color Cyan
        } else {
            Write-Log "Domain did NOT respond properly to initial HTTP(S) request. Status: $($basicRequest.StatusCode)" -Color Red
        }
    } catch {
        Write-Log "Error during HTTP connectivity test: $($_.Exception.Message)" -Color Red
        Write-Log "=== Recon Aborted: Target Unreachable ===" -ForegroundColor Red
        Add-Content -Path $LogFile -Value "=== Recon Aborted: Target Unreachable ==="
        exit
    }
} catch {
    Write-Log "Error fetching basic domain info: $($_.Exception.Message)" -Color Red
    if ($_.Exception.InnerException) {
        Write-Log "Inner Exception: $($_.Exception.InnerException.Message)" -Color Red
    }
    Write-Log "=== Recon Continuing with Limited Information ===" -Color Yellow
}

# --- robots.txt Analysis ---
Write-Log "robots.txt Analysis" -SectionTitle
$robotsUrl = $Target + "robots.txt"
$robotsContent = $null
$disallowedPaths = @()

try {
    $robotsResponse = Invoke-SafeWebRequest -Uri $robotsUrl -TimeoutSec 10
    if ($robotsResponse.StatusCode -eq 200) {
        Write-Log "robots.txt found and fetched successfully." -Color Green

        # Save robots.txt to a file
        $robotsFilePath = Join-Path -Path $OutputDir -ChildPath "robots_$($uri.Host)_$Timestamp.txt"
        $robotsResponse.Content | Out-File -FilePath $robotsFilePath -Encoding utf8
        Write-Log "robots.txt saved to: $robotsFilePath" -Color Cyan

        # Display content
        Write-Log "Content:" -Color Cyan
        $robotsResponse.Content | Out-String | Tee-Object -Variable robotsContent | ForEach-Object {
            # Truncate long lines for display
            $lines = $_ -split "`n"
            foreach ($line in $lines) {
                if ($line.Length -gt 100) {
                    Write-Log "$($line.Substring(0, 100))..." -Color Gray
                } else {
                    Write-Log $line -Color Gray
                }
            }
        }

        # Parse robots.txt for disallowed paths
        $robotsContent -split "`n" | ForEach-Object {
            $line = $_.Trim()

            # Skip comments and empty lines
            if ($line -match "^#" -or $line -eq "") {
                return
            }

            # Extract Disallow
            if ($line -match "^Disallow:\s*(.*)") {
                $path = $matches[1].Trim()
                if ($path -and -not $disallowedPaths.Contains($path)) {
                    $disallowedPaths += $path
                }
            }
        }

        # Report and probe disallowed paths
        if ($disallowedPaths.Count -gt 0) {
            Write-Log "Disallowed paths found ($($disallowedPaths.Count)):" -Color Yellow

            foreach ($path in $disallowedPaths) {
                # Skip broad disallows for probing
                if ($path -eq "/" -or $path -eq "" -or $path -eq "*") {
                    Write-Log "  Path: '$path' - Broad disallow, skipping probe" -Color DarkYellow
                    continue
                }

                Write-Log "  Path: '$path'" -Color Yellow

                # Probe the path
                if (-not $path.StartsWith("/")) {
                    $path = "/" + $path
                }

                $probeUrl = $Target.TrimEnd('/') + $path
                Write-Log "    Probing: $probeUrl" -NoNewLine

                try {
                    $probeResponse = Invoke-SafeWebRequest -Uri $probeUrl -Method HEAD -TimeoutSec 5

                    if ($probeResponse.StatusCode -ge 200 -and $probeResponse.StatusCode -lt 400) {
                        Write-Log " -> [ACCESSIBLE] Status: $($probeResponse.StatusCode)" -Color Red -Vulnerability
                    } else {
                        Write-Log " -> Status: $($probeResponse.StatusCode)" -Color Gray
                    }
                } catch {
                    if ($_.Exception.Response) {
                        Write-Log " -> Status: $($_.Exception.Response.StatusCode)" -Color Gray
                    } else {
                        Write-Log " -> Error: $($_.Exception.Message)" -Color Red
                    }
                }
            }
        } else {
            Write-Log "No 'Disallow' entries found in robots.txt." -Color Green
        }
    } else {
        Write-Log "robots.txt not found or inaccessible (Status: $($robotsResponse.StatusCode))." -Color Yellow
    }
} catch {
    Write-Log "Error fetching robots.txt: $($_.Exception.Message)" -Color Red
}

# --- HTTP Headers Analysis ---
Write-Log "HTTP Headers Analysis" -SectionTitle

# Define security headers to check
$securityHeaders = @{
    "Content-Security-Policy" = "Helps prevent XSS attacks by specifying valid sources of content"
    "Strict-Transport-Security" = "Forces browsers to use HTTPS for future visits"
    "X-Frame-Options" = "Prevents clickjacking attacks by disallowing framing"
    "X-Content-Type-Options" = "Prevents MIME-sniffing attacks"
    "Referrer-Policy" = "Controls how much referrer information is included with requests"
    "X-XSS-Protection" = "Enables XSS filtering in browsers (legacy)"
}

try {
    $headers = @{
        "User-Agent" = $UserAgent
        "Accept" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
        "Accept-Language" = "en-US,en;q=0.5"
        "Accept-Encoding" = "gzip, deflate, br"
        "DNT" = "1"
        "Connection" = "keep-alive"
        "Upgrade-Insecure-Requests" = "1"
    }

    $response = Invoke-SafeWebRequest -Uri $Target -Method GET -Headers $headers -TimeoutSec 15

    if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
        Write-Log "Status Code: $($response.StatusCode) $($response.StatusDescription)" -Color Green

        Write-Log "Headers:" -Color Cyan
        foreach ($key in $response.Headers.Keys) {
            $value = $response.Headers[$key]
            Write-Log "  $key`: $value" -Color Gray

            # Technology detection from headers
            if ($key -match "Server|X-Powered-By|X-AspNet-Version|X-Generator") {
                Write-Log "    [TECH] Potential technology: $key = $value" -Color Cyan
                $script:TechStack += "$key`: $value"
            }

            # Check for information disclosure in headers
            if ($key -match "X-AspNet-Version|X-Runtime|X-Powered-By|X-Generator|X-Debug|Server") {
                Write-Log "    [INFO] Potentially leaking technology information" -Color Yellow -Vulnerability
            }
        }

        # Check for missing security headers
        Write-Log "Missing Security Headers:" -Color Yellow
        $missingHeaders = 0

        foreach ($header in $securityHeaders.Keys) {
            if (-not $response.Headers.ContainsKey($header)) {
                Write-Log "  [!] Missing header: $header" -Color Red
                Write-Log "      Purpose: $($securityHeaders[$header])" -Color Gray
                Write-Log "      [VULNERABILITY] Missing critical security header: $header" -Color Red -Vulnerability
                $missingHeaders++
            }
        }

        if ($missingHeaders -eq 0) {
            Write-Log "  [+] All security headers are present" -Color Green
        }
    } else {
        Write-Log "Non-success status code: $($response.StatusCode)" -Color Yellow
    }
} catch {
    Write-Log "Error fetching HTTP headers: $($_.Exception.Message)" -Color Red
}

# --- CORS Misconfiguration Test ---
Write-Log "CORS Misconfiguration Test" -SectionTitle

# Define multiple origins to test with
$testOrigins = @(
    "https://evil.com",
    "https://attacker.org",
    "null",
    "https://$($uri.Host).evil.com"
)

# Track CORS findings
$corsVulnerabilities = @()

foreach ($origin in $testOrigins) {
    Write-Log "Testing with Origin: $origin" -Color Cyan

    try {
        $headers = @{
            "Origin" = $origin
            "User-Agent" = $UserAgent
            "Accept" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
        }

        $corsResponse = Invoke-SafeWebRequest -Uri $Target -Method GET -Headers $headers -TimeoutSec 10

        if ($corsResponse.StatusCode -ge 200 -and $corsResponse.StatusCode -lt 400) {
            Write-Log "  Response Status: $($corsResponse.StatusCode)" -Color Green

            # Check CORS headers
            $acaOrigin = if ($corsResponse.Headers.ContainsKey("Access-Control-Allow-Origin")) { $corsResponse.Headers["Access-Control-Allow-Origin"] } else { $null }
            $acaCredentials = if ($corsResponse.Headers.ContainsKey("Access-Control-Allow-Credentials")) { $corsResponse.Headers["Access-Control-Allow-Credentials"] } else { $null }

            Write-Log "  CORS Headers:" -Color Cyan
            if ($acaOrigin) { Write-Log "    Access-Control-Allow-Origin: $acaOrigin" -Color Yellow }
            if ($acaCredentials) { Write-Log "    Access-Control-Allow-Credentials: $acaCredentials" -Color Yellow }

            # Analyze CORS configuration
            if ($acaOrigin) {
                if ($acaOrigin -eq "*") {
                    Write-Log "    [FINDING] Wildcard CORS origin (*)" -Color Yellow

                    if ($acaCredentials -eq "true") {
                        Write-Log "    [VULNERABILITY] Wildcard origin with credentials (invalid configuration)" -Color Red -Vulnerability
                        $corsVulnerabilities += "Wildcard CORS origin (*) with credentials allowed"
                    } else {
                        Write-Log "    [INFO] Wildcard CORS without credentials (less severe)" -Color Yellow
                    }
                }
                elseif ($acaOrigin -eq $origin) {
                    Write-Log "    [VULNERABILITY] Origin is reflected: $acaOrigin" -Color Red -Vulnerability

                    if ($acaCredentials -eq "true") {
                        Write-Log "    [CRITICAL] Arbitrary origin reflection WITH credentials" -Color Red -Vulnerability
                        $corsVulnerabilities += "Arbitrary origin reflection ($origin) with credentials allowed"
                    } else {
                        $corsVulnerabilities += "Arbitrary origin reflection ($origin)"
                    }
                }
                else {
                    Write-Log "    [OK] Origin not reflected for: $origin" -Color Green
                }
            } else {
                Write-Log "    [OK] No CORS headers returned for origin: $origin" -Color Green
            }
        } else {
            Write-Log "  Request failed: $($corsResponse.StatusCode)" -Color Yellow
        }
    } catch {
        Write-Log "  Error testing CORS with origin '$origin': $($_.Exception.Message)" -Color Red
    }

    Write-Log "" # Add a blank line between tests
}

# Summary of CORS findings
if ($corsVulnerabilities.Count -gt 0) {
    Write-Log "CORS Vulnerabilities Summary:" -Color Red
    foreach ($vuln in $corsVulnerabilities) {
        Write-Log "  - $vuln" -Color Red
    }
} else {
    Write-Log "No CORS vulnerabilities detected" -Color Green
}

# --- Common API Endpoint Scan ---
Write-Log "Common API Endpoint Scan" -SectionTitle

# Define API endpoints to test based on intensity level
$commonApiEndpoints = @()

# Basic API endpoints (always tested)
$basicApiEndpoints = @(
    "api", "api/v1", "api/v2", "api/v3", "api/graphql",
    "api/users", "api/user", "api/auth", "api/login", "api/register",
    "graphql", "graph", "gql", "query",
    "rest", "rest-api", "wp-json", "wp-json/wp/v2",
    "v1", "v2", "v3"
)
$commonApiEndpoints += $basicApiEndpoints

# Additional endpoints for higher intensity scans
if ($Intensity -ge 2) {
    $mediumApiEndpoints = @(
        "api/admin", "api/config", "api/settings", "api/system",
        "api/status", "api/health", "api/metrics", "api/stats",
        "api/test", "api/debug", "api/dev", "api/internal",
        "api/public", "api/private", "api/open", "api/docs",
        "api/swagger", "api/openapi", "api/spec",
        "api/data", "api/search", "api/query", "api/filter"
    )
    $commonApiEndpoints += $mediumApiEndpoints
}

# Remove duplicates
$commonApiEndpoints = $commonApiEndpoints | Select-Object -Unique

# Define HTTP methods to test
$httpMethodsToTest = @("GET", "HEAD")

# Add more methods for higher intensity scans
if ($Intensity -ge 2) {
    $httpMethodsToTest += @("POST", "OPTIONS")
}

# Track discovered endpoints
$discoveredEndpoints = @()
$vulnerableEndpoints = @()

# Test each endpoint
foreach ($endpoint in $commonApiEndpoints) {
    $apiUrl = $Target.TrimEnd('/') + "/" + $endpoint.TrimStart('/')
    Write-Log "Probing API Endpoint: $apiUrl" -Color Cyan

    $endpointResponded = $false

    foreach ($method in $httpMethodsToTest) {
        Write-Log "  Testing method: $method ..." -NoNewLine

        try {
            # Add custom headers that might help with API discovery
            $headers = @{
                "User-Agent" = $UserAgent
                "Accept" = "application/json, text/plain, */*"
                "X-Requested-With" = "XMLHttpRequest"
            }

            # For POST/PUT/PATCH, add a minimal JSON body
            $body = $null
            if ($method -in @("POST", "PUT", "PATCH")) {
                $headers["Content-Type"] = "application/json"
                $body = '{"test":"test"}'
            }

            # Make the request
            $params = @{
                Uri = $apiUrl
                Method = $method
                Headers = $headers
                TimeoutSec = 10
            }

            if ($body) {
                $params["Body"] = $body
            }

            $apiResponse = Invoke-SafeWebRequest @params

            # Determine status color
            $statusColor = switch ($apiResponse.StatusCode) {
                { $_ -ge 200 -and $_ -lt 300 } { "Green" }
                { $_ -ge 300 -and $_ -lt 400 } { "Cyan" }
                { $_ -ge 400 -and $_ -lt 500 } { "Yellow" }
                { $_ -ge 500 } { "Red" }
                default { "Gray" }
            }

            Write-Log " -> Status: $($apiResponse.StatusCode)" -Color $statusColor

            # If we got a successful response, analyze it
            if ($apiResponse.StatusCode -ge 200 -and $apiResponse.StatusCode -lt 400) {
                $endpointResponded = $true
                $discoveredEndpoints += @{
                    Url = $apiUrl
                    Method = $method
                    StatusCode = $apiResponse.StatusCode
                }

                # Check for CORS headers
                if ($apiResponse.Headers.ContainsKey("Access-Control-Allow-Origin")) {
                    $corsValue = $apiResponse.Headers["Access-Control-Allow-Origin"]
                    Write-Log "    [CORS] Access-Control-Allow-Origin: $corsValue" -Color Yellow

                    if ($corsValue -eq "*") {
                        Write-Log "    [FINDING] API has wildcard CORS policy" -Color Red
                    }
                }

                # For OPTIONS requests, check allowed methods
                if ($method -eq "OPTIONS" -and $apiResponse.Headers.ContainsKey("Allow")) {
                    $allowedMethods = $apiResponse.Headers["Allow"]
                    Write-Log "    [OPTIONS] Allowed Methods: $allowedMethods" -Color Cyan

                    # Check for dangerous methods
                    if ($allowedMethods -match "PUT|DELETE|PATCH") {
                        Write-Log "    [FINDING] Potentially dangerous methods allowed: $allowedMethods" -Color Red
                    }
                }
            }
            elseif ($apiResponse.StatusCode -eq 401 -or $apiResponse.StatusCode -eq 403) {
                # Authentication/authorization required - endpoint exists but protected
                Write-Log "    [PROTECTED] Endpoint requires authentication" -Color Yellow
                $endpointResponded = $true
                $discoveredEndpoints += @{
                    Url = $apiUrl
                    Method = $method
                    StatusCode = $apiResponse.StatusCode
                    Protected = $true
                }
            }
        } catch {
            if ($_.Exception.Response) {
                $statusCode = $_.Exception.Response.StatusCode.value__
                $statusColor = if ($statusCode -eq 401 -or $statusCode -eq 403) { "Yellow" } elseif ($statusCode -ge 500) { "Red" } else { "Gray" }
                Write-Log " -> Status: $statusCode" -Color $statusColor

                # Authentication/authorization required - endpoint exists but protected
                if ($statusCode -eq 401 -or $statusCode -eq 403) {
                    Write-Log "    [PROTECTED] Endpoint requires authentication" -Color Yellow
                    $endpointResponded = $true
                    $discoveredEndpoints += @{
                        Url = $apiUrl
                        Method = $method
                        StatusCode = $statusCode
                        Protected = $true
                    }
                }
            } else {
                Write-Log " -> Error: $($_.Exception.Message)" -Color Red
            }
        }
    }

    # Add a blank line between endpoints for readability
    Write-Log ""
}

# Summary of API findings
Write-Log "API Endpoint Scan Summary:" -Color Cyan
if ($discoveredEndpoints.Count -gt 0) {
    Write-Log "  Discovered $($discoveredEndpoints.Count) accessible API endpoints:" -Color Green

    # Group by status code for better organization
    $groupedEndpoints = $discoveredEndpoints | Group-Object -Property StatusCode

    foreach ($group in $groupedEndpoints) {
        $statusDesc = switch ($group.Name) {
            200 { "OK" }
            201 { "Created" }
            204 { "No Content" }
            301 { "Moved Permanently" }
            302 { "Found" }
            304 { "Not Modified" }
            400 { "Bad Request" }
            401 { "Unauthorized" }
            403 { "Forbidden" }
            404 { "Not Found" }
            405 { "Method Not Allowed" }
            500 { "Internal Server Error" }
            default { "" }
        }

        Write-Log "  Status $($group.Name) ($statusDesc):" -Color Yellow
        foreach ($endpoint in $group.Group) {
            $protectedTag = if ($endpoint.Protected) { " [PROTECTED]" } else { "" }
            Write-Log "    - $($endpoint.Method) $($endpoint.Url)$protectedTag" -Color Green
        }
    }
} else {
    Write-Log "  No accessible API endpoints discovered." -Color Yellow
}

# --- XSS Reflection Testing ---
Write-Log "XSS Reflection Testing" -SectionTitle

# Define XSS payloads with increasing complexity based on intensity level
$xssPayloads = @()

# Basic payloads (always included)
$basicPayloads = @(
    "<script>alert('XSS')</script>",
    "'><img src=x onerror=alert('XSS')>",
    "<svg/onload=alert(1)>",
    "javascript:alert('XSS')"
)
$xssPayloads += $basicPayloads

# Medium intensity payloads
if ($Intensity -ge 2) {
    $mediumPayloads = @(
        # Obfuscated payloads
        "<img src=x onerror=eval(atob('YWxlcnQoJ1hTUycp'))>",
        "<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>",
        # Event handlers
        "<body onload=alert('XSS')>",
        "<iframe onload=alert('XSS')></iframe>",
        # AngularJS
        "{{constructor.constructor('alert(`"XSS`")')()}}"
    )
    $xssPayloads += $mediumPayloads
}

# Common parameters to test
$commonParams = @(
    # Search related
    "q", "s", "query", "search", "keyword", "term", "filter",
    # Navigation related
    "id", "page", "p", "url", "link", "goto", "redirect", "next", "prev", "return", "returnUrl", "return_url", "redirect_to", "redirectTo",
    # User input
    "name", "username", "user", "email", "comment", "message", "content", "title", "description",
    # Configuration
    "lang", "language", "locale", "theme", "view", "mode", "display", "format",
    # Other common
    "callback", "jsonp", "action", "type", "category", "tag", "sort", "order"
)

# Track findings
$xssVulnerabilities = @()
$reflectedParameters = @()

Write-Log "Testing $($commonParams.Count) parameters with $($xssPayloads.Count) XSS payloads..." -Color Cyan

# Limit the number of parameters to test based on intensity
$maxParams = switch ($Intensity) {
    1 { 10 }  # Basic - test up to 10 parameters
    2 { 20 }  # Standard - test up to 20 parameters
    3 { 50 }  # Aggressive - test up to 50 parameters
    default { 10 }
}

$allTestParams = $commonParams | Select-Object -First $maxParams

# Test each parameter with each payload
foreach ($paramName in $allTestParams) {
    Write-Log "Testing parameter: $paramName" -Color Cyan
    $parameterVulnerable = $false
    $parameterReflected = $false

    foreach ($payload in $xssPayloads) {
        # URL encode the payload
        $encodedPayload = [System.Web.HttpUtility]::UrlEncode($payload)
        $testUrl = $Target.TrimEnd('/') + "/?$paramName=$encodedPayload"

        Write-Log "  Testing payload: $payload" -NoNewLine

        try {
            $xssResponse = Invoke-SafeWebRequest -Uri $testUrl -TimeoutSec 10

            if ($xssResponse.StatusCode -ge 200 -and $xssResponse.StatusCode -lt 400) {
                $responseContent = $xssResponse.Content.ToString()

                # Check for direct reflection (potential XSS)
                if ($responseContent -match [regex]::Escape($payload)) {
                    Write-Log " -> [VULNERABLE] Payload reflected unencoded!" -Color Red -Vulnerability
                    $parameterVulnerable = $true
                    $parameterReflected = $true

                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "High"
                        Details = "Payload reflected without encoding"
                    }
                }
                # Check for HTML-encoded reflection (less severe)
                elseif ($responseContent -match [regex]::Escape([System.Web.HttpUtility]::HtmlEncode($payload))) {
                    Write-Log " -> [REFLECTED] Payload reflected but HTML-encoded" -Color Yellow
                    $parameterReflected = $true

                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "Medium"
                        Details = "Payload reflected with HTML encoding"
                    }
                }
                # Check for URL-encoded reflection
                elseif ($responseContent -match [regex]::Escape($encodedPayload)) {
                    Write-Log " -> [ENCODED] Payload reflected URL-encoded" -Color Yellow
                    $parameterReflected = $true

                    $xssVulnerabilities += @{
                        Parameter = $paramName
                        Payload = $payload
                        URL = $testUrl
                        Severity = "Low"
                        Details = "Payload reflected URL-encoded"
                    }
                }
                else {
                    Write-Log " -> No reflection detected" -Color Green
                }

                # If we've already found a vulnerability for this parameter, skip remaining payloads
                if ($parameterVulnerable -and $Intensity -lt 3) {
                    break
                }
            } else {
                Write-Log " -> Status: $($xssResponse.StatusCode)" -Color Gray
            }
        } catch {
            if ($_.Exception.Response) {
                Write-Log " -> Error: $($_.Exception.Response.StatusCode)" -Color Red
            } else {
                Write-Log " -> Error: $($_.Exception.Message)" -Color Red
            }
        }
    }

    # Track reflected parameters for summary
    if ($parameterReflected) {
        $reflectedParameters += $paramName
    }

    # Add a blank line between parameters for readability
    Write-Log ""
}

# Summary of XSS findings
Write-Log "XSS Testing Summary:" -Color Cyan
if ($xssVulnerabilities.Count -gt 0) {
    Write-Log "Found $($xssVulnerabilities.Count) potential XSS vulnerabilities across $($reflectedParameters.Count) parameters:" -Color Red

    # Group by severity
    $highSeverity = $xssVulnerabilities | Where-Object { $_.Severity -eq "High" }
    $mediumSeverity = $xssVulnerabilities | Where-Object { $_.Severity -eq "Medium" }
    $lowSeverity = $xssVulnerabilities | Where-Object { $_.Severity -eq "Low" }

    if ($highSeverity.Count -gt 0) {
        Write-Log "  High Severity ($($highSeverity.Count)):" -Color Red
        foreach ($vuln in $highSeverity) {
            Write-Log "    - Parameter: $($vuln.Parameter)" -Color Red -Vulnerability
            Write-Log "      Payload: $($vuln.Payload)" -Color Red
            Write-Log "      URL: $($vuln.URL)" -Color Red
            Write-Log "      Details: $($vuln.Details)" -Color Red
        }
    }

    if ($mediumSeverity.Count -gt 0) {
        Write-Log "  Medium Severity ($($mediumSeverity.Count)):" -Color Yellow
        foreach ($vuln in $mediumSeverity | Select-Object -First 5) {
            Write-Log "    - Parameter: $($vuln.Parameter)" -Color Yellow -Vulnerability
            Write-Log "      Details: $($vuln.Details)" -Color Yellow
        }
        if ($mediumSeverity.Count -gt 5) {
            Write-Log "    ... and $($mediumSeverity.Count - 5) more" -Color Yellow
        }
    }

    if ($lowSeverity.Count -gt 0) {
        Write-Log "  Low Severity ($($lowSeverity.Count)):" -Color Yellow
        $lowParams = ($lowSeverity.Parameter | Select-Object -Unique) -join ', '
        Write-Log "    Parameters with low severity findings: $lowParams" -Color Yellow
    }
} else {
    Write-Log "No XSS vulnerabilities detected in tested parameters." -Color Green
}

# --- Exposed Files and Directories Scan ---
Write-Log "Exposed Files and Directories Scan" -SectionTitle

# Define common exposed files and directories
$exposedFiles = @(
    # Environment and configuration files
    ".env", ".env.local", ".env.production", ".env.development",
    "config.php", "config.json", "config.xml", "config.yml", "config.yaml",
    "settings.php", "settings.json", "settings.xml",
    "wp-config.php", "wp-config.php.bak", "wp-config.php.old",
    "database.yml", "database.json", "database.xml",

    # Version control
    ".git/", ".git/config", ".git/HEAD", ".git/logs/HEAD",
    ".svn/", ".svn/entries", ".hg/", ".bzr/",

    # Backup files
    "backup.sql", "backup.zip", "backup.tar.gz", "backup.tar",
    "database.sql", "db.sql", "dump.sql", "mysql.sql",
    "site.zip", "website.zip", "www.zip", "backup.bak",

    # Admin and management
    "admin/", "admin.php", "administrator/", "manage/", "management/",
    "phpmyadmin/", "pma/", "mysql/", "adminer.php",
    "wp-admin/", "wp-login.php", "wp-includes/",

    # Documentation and info
    "readme.txt", "README.md", "CHANGELOG.md", "INSTALL.txt",
    "phpinfo.php", "info.php", "test.php", "debug.php",

    # Server files
    ".htaccess", ".htpasswd", "web.config", "nginx.conf",
    "httpd.conf", "apache.conf", ".user.ini",

    # Application specific
    "composer.json", "package.json", "yarn.lock", "package-lock.json",
    "Gemfile", "requirements.txt", "pom.xml", "build.gradle",

    # Logs
    "error.log", "access.log", "debug.log", "application.log",
    "logs/", "log/", "var/log/", "tmp/",

    # Common directories
    "uploads/", "files/", "images/", "assets/", "static/",
    "public/", "private/", "temp/", "tmp/", "cache/"
)

# Add more files for higher intensity scans
if ($Intensity -ge 2) {
    $additionalFiles = @(
        # More backup patterns
        "backup_$(Get-Date -Format 'yyyy').sql", "backup_$(Get-Date -Format 'yyyyMM').sql",
        "site_backup.zip", "full_backup.tar.gz", "complete_backup.zip",

        # More config files
        "local_settings.py", "production_settings.py", "development_settings.py",
        "app.config", "appsettings.json", "connectionstrings.config",

        # More admin panels
        "admin/login.php", "admin/index.php", "admin/dashboard.php",
        "administrator/index.php", "manage/login.php",

        # More logs
        "error_log", "access_log", "php_errors.log", "laravel.log",
        "application.log", "system.log", "security.log",

        # Development files
        "test/", "tests/", "testing/", "dev/", "development/",
        "staging/", "beta/", "demo/", "sandbox/"
    )
    $exposedFiles += $additionalFiles
}

# Remove duplicates
$exposedFiles = $exposedFiles | Select-Object -Unique

# Track findings
$exposedFindings = @()
$criticalExposures = @()

Write-Log "Testing $($exposedFiles.Count) common exposed files and directories..." -Color Cyan

# Test each file/directory
foreach ($file in $exposedFiles) {
    $fileUrl = $Target.TrimEnd('/') + "/" + $file.TrimStart('/')
    Write-Log "Checking: $file" -NoNewLine

    try {
        $fileResponse = Invoke-SafeWebRequest -Uri $fileUrl -Method HEAD -TimeoutSec 8

        if ($fileResponse.StatusCode -ge 200 -and $fileResponse.StatusCode -lt 400) {
            $severity = "Medium"
            $description = "File/directory accessible"

            # Determine severity based on file type
            if ($file -match "\.(env|config|sql|bak|old|backup|log)$|\.git/|\.svn/|wp-config|database\.|admin|phpmyadmin") {
                $severity = "High"
                $description = "Sensitive file/directory exposed"
                $criticalExposures += $file
            }

            Write-Log " -> [EXPOSED] Status: $($fileResponse.StatusCode) ($severity)" -Color (if ($severity -eq "High") { "Red" } else { "Yellow" }) -Vulnerability

            $exposedFindings += @{
                File = $file
                URL = $fileUrl
                StatusCode = $fileResponse.StatusCode
                Severity = $severity
                Description = $description
            }

            # For GET-able files, try to get content length
            if ($fileResponse.Headers -and $fileResponse.Headers["Content-Length"]) {
                $contentLength = $fileResponse.Headers["Content-Length"]
                Write-Log "    Size: $contentLength bytes" -Color Gray
            }
        }
        elseif ($fileResponse.StatusCode -eq 403) {
            Write-Log " -> [FORBIDDEN] Directory listing disabled (good)" -Color Yellow
        }
        else {
            Write-Log " -> Status: $($fileResponse.StatusCode)" -Color Green
        }
    } catch {
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode.value__
            if ($statusCode -eq 403) {
                Write-Log " -> [FORBIDDEN] Directory listing disabled (good)" -Color Yellow
            } else {
                Write-Log " -> Status: $statusCode" -Color Green
            }
        } else {
            Write-Log " -> Not accessible" -Color Green
        }
    }
}

# Summary of exposed files findings
Write-Log "Exposed Files Scan Summary:" -Color Cyan
if ($exposedFindings.Count -gt 0) {
    Write-Log "Found $($exposedFindings.Count) exposed files/directories:" -Color Red

    # Group by severity
    $highSeverity = $exposedFindings | Where-Object { $_.Severity -eq "High" }
    $mediumSeverity = $exposedFindings | Where-Object { $_.Severity -eq "Medium" }

    if ($highSeverity.Count -gt 0) {
        Write-Log "  Critical Exposures ($($highSeverity.Count)):" -Color Red
        foreach ($finding in $highSeverity) {
            Write-Log "    - $($finding.File) (Status: $($finding.StatusCode))" -Color Red -Vulnerability
            Write-Log "      URL: $($finding.URL)" -Color Red
        }
    }

    if ($mediumSeverity.Count -gt 0) {
        Write-Log "  Medium Severity ($($mediumSeverity.Count)):" -Color Yellow
        foreach ($finding in $mediumSeverity | Select-Object -First 10) {
            Write-Log "    - $($finding.File) (Status: $($finding.StatusCode))" -Color Yellow
        }
        if ($mediumSeverity.Count -gt 10) {
            Write-Log "    ... and $($mediumSeverity.Count - 10) more" -Color Yellow
        }
    }
} else {
    Write-Log "No exposed files or directories detected." -Color Green
}

# --- SSL/TLS Certificate Analysis ---
Write-Log "SSL/TLS Certificate Analysis" -SectionTitle

if ($uri.Scheme -eq "https") {
    try {
        Write-Log "Analyzing SSL/TLS certificate for $($uri.Host)..." -Color Cyan

        # Create a TCP client to get certificate information
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.Connect($uri.Host, $uri.Port)

        # Create SSL stream
        $sslStream = New-Object System.Net.Security.SslStream($tcpClient.GetStream())
        $sslStream.AuthenticateAsClient($uri.Host)

        # Get certificate
        $cert = $sslStream.RemoteCertificate
        $cert2 = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($cert)

        Write-Log "Certificate Information:" -Color Cyan
        Write-Log "  Subject: $($cert2.Subject)" -Color Green
        Write-Log "  Issuer: $($cert2.Issuer)" -Color Green
        Write-Log "  Valid From: $($cert2.NotBefore)" -Color Green
        Write-Log "  Valid Until: $($cert2.NotAfter)" -Color Green
        Write-Log "  Serial Number: $($cert2.SerialNumber)" -Color Green
        Write-Log "  Thumbprint: $($cert2.Thumbprint)" -Color Green
        Write-Log "  Signature Algorithm: $($cert2.SignatureAlgorithm.FriendlyName)" -Color Green

        # Check certificate validity
        $now = Get-Date
        $daysUntilExpiry = ($cert2.NotAfter - $now).Days

        if ($daysUntilExpiry -lt 0) {
            Write-Log "  [CRITICAL] Certificate has EXPIRED!" -Color Red -Vulnerability
        } elseif ($daysUntilExpiry -lt 30) {
            Write-Log "  [WARNING] Certificate expires in $daysUntilExpiry days" -Color Yellow -Vulnerability
        } else {
            Write-Log "  [OK] Certificate expires in $daysUntilExpiry days" -Color Green
        }

        # Check for weak signature algorithms
        if ($cert2.SignatureAlgorithm.FriendlyName -match "SHA1|MD5") {
            Write-Log "  [VULNERABILITY] Weak signature algorithm: $($cert2.SignatureAlgorithm.FriendlyName)" -Color Red -Vulnerability
        }

        # Check key size
        if ($cert2.PublicKey.Key.KeySize -lt 2048) {
            Write-Log "  [VULNERABILITY] Weak key size: $($cert2.PublicKey.Key.KeySize) bits" -Color Red -Vulnerability
        } else {
            Write-Log "  [OK] Key size: $($cert2.PublicKey.Key.KeySize) bits" -Color Green
        }

        # Check Subject Alternative Names (SAN)
        $sanExtension = $cert2.Extensions | Where-Object { $_.Oid.FriendlyName -eq "Subject Alternative Name" }
        if ($sanExtension) {
            Write-Log "  Subject Alternative Names:" -Color Cyan
            $sanExtension.Format($false) -split ", " | ForEach-Object {
                Write-Log "    - $_" -Color Gray
            }
        }

        # Clean up
        $sslStream.Close()
        $tcpClient.Close()

    } catch {
        Write-Log "Error analyzing SSL certificate: $($_.Exception.Message)" -Color Red
    }
} else {
    Write-Log "Target uses HTTP (not HTTPS) - no SSL certificate to analyze" -Color Yellow
    Write-Log "[SECURITY ISSUE] Site does not use HTTPS encryption" -Color Red -Vulnerability
}

# --- Technology Detection Summary ---
Write-Log "Technology Detection Summary" -SectionTitle

if ($TechStack.Count -gt 0) {
    Write-Log "Detected Technologies:" -Color Cyan
    $TechStack | Select-Object -Unique | ForEach-Object {
        Write-Log "  - $_" -Color Green
    }
} else {
    Write-Log "No specific technologies detected from headers" -Color Yellow
}

# --- HTTP Method Testing ---
if ($Intensity -ge 2) {
    Write-Log "HTTP Method Testing" -SectionTitle

    $httpMethods = @("OPTIONS", "TRACE", "TRACK", "PUT", "DELETE", "PATCH", "CONNECT")
    $dangerousMethods = @()

    foreach ($method in $httpMethods) {
        Write-Log "Testing method: $method" -NoNewLine

        try {
            $methodResponse = Invoke-SafeWebRequest -Uri $Target -Method $method -TimeoutSec 10

            if ($methodResponse.StatusCode -ge 200 -and $methodResponse.StatusCode -lt 400) {
                Write-Log " -> [ALLOWED] Status: $($methodResponse.StatusCode)" -Color Red
                $dangerousMethods += $method

                if ($method -eq "TRACE" -and $methodResponse.Content -match "TRACE") {
                    Write-Log "    [VULNERABILITY] HTTP TRACE method enabled (XST possible)" -Color Red -Vulnerability
                }
            } else {
                Write-Log " -> Status: $($methodResponse.StatusCode)" -Color Green
            }
        } catch {
            if ($_.Exception.Response) {
                Write-Log " -> Status: $($_.Exception.Response.StatusCode.value__)" -Color Green
            } else {
                Write-Log " -> Not allowed" -Color Green
            }
        }
    }

    if ($dangerousMethods.Count -gt 0) {
        Write-Log "Potentially dangerous HTTP methods allowed: $($dangerousMethods -join ', ')" -Color Red -Vulnerability
    } else {
        Write-Log "No dangerous HTTP methods detected" -Color Green
    }
}

# --- Final Summary ---
$endTime = Get-Date
$duration = $endTime - $StartTime

Write-Log "=== RECONNAISSANCE COMPLETE ===" -SectionTitle

Write-Log "Scan Summary:" -Color Cyan
Write-Log "  Target: $Target" -Color White
Write-Log "  Duration: $($duration.ToString('hh\:mm\:ss'))" -Color White
Write-Log "  Intensity Level: $Intensity" -Color White
Write-Log "  Log File: $LogFile" -Color White

# Vulnerability Summary
if ($FoundVulnerabilities.Count -gt 0) {
    Write-Log "Security Findings ($($FoundVulnerabilities.Count)):" -Color Red
    $FoundVulnerabilities | Select-Object -Unique | ForEach-Object {
        Write-Log "  [!] $_" -Color Red
    }

    Write-Log "`n[IMPORTANT] This scan found potential security issues." -Color Red
    Write-Log "Please review the findings above and take appropriate action." -Color Red
    Write-Log "Remember: This tool is for authorized testing only." -Color Yellow
} else {
    Write-Log "No obvious security vulnerabilities detected in this scan." -Color Green
    Write-Log "Note: This does not guarantee the target is secure." -Color Yellow
}

Write-Log "`nRecommendations:" -Color Cyan
Write-Log "  1. Review all findings marked as vulnerabilities" -Color White
Write-Log "  2. Implement missing security headers" -Color White
Write-Log "  3. Remove or protect exposed sensitive files" -Color White
Write-Log "  4. Ensure HTTPS is properly configured" -Color White
Write-Log "  5. Regularly update and patch all software" -Color White
Write-Log "  6. Conduct regular security assessments" -Color White

Write-Log "`n=== END OF REPORT ===" -Color Cyan

# Save a summary to a separate file
$summaryFile = Join-Path -Path $OutputDir -ChildPath "ReconSummary_$Timestamp.txt"
$summaryContent = @"
=== RECONNAISSANCE SUMMARY ===
Target: $Target
Scan Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Duration: $($duration.ToString('hh\:mm\:ss'))
Intensity: $Intensity

VULNERABILITIES FOUND: $($FoundVulnerabilities.Count)
$(if ($FoundVulnerabilities.Count -gt 0) {
    $FoundVulnerabilities | Select-Object -Unique | ForEach-Object { "- $_" }
} else {
    "No obvious vulnerabilities detected"
})

TECHNOLOGIES DETECTED:
$(if ($TechStack.Count -gt 0) {
    $TechStack | Select-Object -Unique | ForEach-Object { "- $_" }
} else {
    "No specific technologies detected"
})

Full details available in: $LogFile
=== END OF SUMMARY ===
"@

$summaryContent | Out-File -FilePath $summaryFile -Encoding utf8
Write-Log "Summary saved to: $summaryFile" -Color Cyan

Write-Host "`nScan completed. Check the log files for detailed results." -ForegroundColor Green
