#Requires -Version 5.1

param(
    [Parameter(Mandatory=$true)]
    [string]$Target
)

Write-Host "Testing syntax for: $Target" -ForegroundColor Green

# Test the problematic parts
$testPayloads = @(
    "<svg/onload=confirm(1)>",
    "<script>alert(1)</script>",
    "{{constructor.constructor(`"alert(1)`")()}}"
)

Write-Host "Payloads loaded successfully" -ForegroundColor Green

# Test URL construction
$domain = "example.com"
$crtshUrl = "https://crt.sh/?q=%25.$domain" + "&" + "output=json"
Write-Host "URL: $crtshUrl" -ForegroundColor Cyan

Write-Host "Syntax test completed successfully!" -ForegroundColor Green
