Target: https://httpbin.org/

Log File: C:\Users\<USER>\OneDrive\Stalinis kompiuteris\powershell fun\ReconLog_20250528212343.txt

Timestamp: 2025-05-28 21:23:43

Scan Intensity: 1 (Basic)

Target: https://httpbin.org/
Log File: C:\Users\<USER>\OneDrive\Stalinis kompiuteris\powershell fun\ReconLog_20250528212343.txt
Timestamp: 2025-05-28 21:23:43
Scan Intensity: 1 (Basic)

--- BASIC DOMAIN INFO ---

Domain: httpbin.org

Scheme: https

Port: 443

IPv4 Addresses:

  - *************

  - ***********

  - ************

  - *************

Domain does not respond to ICMP (ping) or ICMP is blocked.

Domain resolves and responds to HTTP(S) requests. Status: 200

  - Page Title: httpbin.org

  - Content Length: 9593 bytes


--- ROBOTS.TXT ANALYSIS ---

robots.txt found and fetched successfully.

robots.txt saved to: C:\Users\<USER>\OneDrive\Stalinis kompiuteris\powershell fun\robots_httpbin.org_20250528212343.txt

Content:

User-agent: *

Disallow: /deny






Disallowed paths found (1):

  Path: '/deny'

    Probing: https://httpbin.org/deny -> [ACCESSIBLE] Status: 200


--- HTTP HEADERS ANALYSIS ---

Non-success status code: 0


--- CORS MISCONFIGURATION TEST ---

Testing with Origin: https://evil.com

  Response Status: 200

  CORS Headers:

    Access-Control-Allow-Origin: https://evil.com

    Access-Control-Allow-Credentials: true

    [VULNERABILITY] Origin is reflected: https://evil.com

    [CRITICAL] Arbitrary origin reflection WITH credentials



Testing with Origin: https://attacker.org

  Response Status: 200

  CORS Headers:

    Access-Control-Allow-Origin: https://attacker.org

    Access-Control-Allow-Credentials: true

    [VULNERABILITY] Origin is reflected: https://attacker.org

    [CRITICAL] Arbitrary origin reflection WITH credentials



Testing with Origin: null

  Response Status: 200

  CORS Headers:

    Access-Control-Allow-Origin: null

    Access-Control-Allow-Credentials: true

    [VULNERABILITY] Origin is reflected: null

    [CRITICAL] Arbitrary origin reflection WITH credentials



Testing with Origin: https://httpbin.org.evil.com

  Response Status: 200

  CORS Headers:

    Access-Control-Allow-Origin: https://httpbin.org.evil.com

    Access-Control-Allow-Credentials: true

    [VULNERABILITY] Origin is reflected: https://httpbin.org.evil.com

    [CRITICAL] Arbitrary origin reflection WITH credentials



CORS Vulnerabilities Summary:

  - Arbitrary origin reflection (https://evil.com) with credentials allowed

  - Arbitrary origin reflection (https://attacker.org) with credentials allowed

  - Arbitrary origin reflection (null) with credentials allowed

  - Arbitrary origin reflection (https://httpbin.org.evil.com) with credentials allowed


--- COMMON API ENDPOINT SCAN ---

Probing API Endpoint: https://httpbin.org/api

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/v1

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/v2

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/v3

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/graphql

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/users

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/user

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/auth

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/login

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/api/register

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/graphql

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/graph

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/gql

  Testing method: GET ... -> Status: 502

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/query

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/rest

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/rest-api

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/wp-json

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/wp-json/wp/v2

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/v1

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/v2

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



Probing API Endpoint: https://httpbin.org/v3

  Testing method: GET ... -> Status: 404

  Testing method: HEAD ... -> Status: 404



API Endpoint Scan Summary:

  No accessible API endpoints discovered.


--- XSS REFLECTION TESTING ---

Testing 46 parameters with 4 XSS payloads...

Testing parameter: q

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: s

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: query

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: search

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: keyword

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: term

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: filter

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: id

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: page

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



Testing parameter: p

  Testing payload: <script>alert('XSS')</script> -> No reflection detected

  Testing payload: '><img src=x onerror=alert('XSS')> -> No reflection detected

  Testing payload: <svg/onload=alert(1)> -> No reflection detected

  Testing payload: javascript:alert('XSS') -> No reflection detected



XSS Testing Summary:

No XSS vulnerabilities detected in tested parameters.


--- EXPOSED FILES AND DIRECTORIES SCAN ---

Testing 90 common exposed files and directories...

Checking: .env -> Status: 404

Checking: .env.local -> Status: 404

Checking: .env.production -> Status: 404

Checking: .env.development -> Status: 404

Checking: config.php -> Status: 404

Checking: config.json -> Status: 404

Checking: config.xml -> Status: 404

Checking: config.yml -> Status: 404

Checking: config.yaml -> Status: 404

Checking: settings.php -> Status: 404

Checking: settings.json -> Status: 404

Checking: settings.xml -> Status: 404

Checking: wp-config.php -> Status: 404

Checking: wp-config.php.bak -> Status: 404

Checking: wp-config.php.old